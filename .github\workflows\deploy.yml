#基于1panel的openresty配置

name: course-web-deploy # 工作流的名称

on:
    push:
        branches:
            - main # 触发的分支，可以根据需要修改为其他分支

jobs:
    deploy:
        name: 基于1panel的openresty配置，直接打包复制最终项目文件即可
        runs-on: ubuntu-latest
        strategy:
            fail-fast: false # 确保所有服务器都尝试部署
            matrix:
                server_ip: [
                  '*************', #优化东京2c2g
                  ]

        steps:
            - name: 1.从仓库中检出当前 commit 的所有文件
              uses: actions/checkout@v3

            - name: 2. 安装依赖
              run: npm install

            - name: 3. 构建项目（打包）
              run: npm run build

            - name: 4.复制未混淆的网站到服务器 coursebak.mozhi0012.top
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ matrix.server_ip }}
                  username: ${{ secrets.SERVER_USER }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
                  source: 'dist/*'
                  target: '/opt/1panel/1panel/www/sites/coursebak.mozhi0012.top/index'
                  overwrite: true
                  # 最终目录就是 target+(source-strip_components)，这里就是/etc/nginx/custom/
                  strip_components: 1

            - name: 5. 代码混淆
              run: npm run obfuscate

            - name: 6. 复制混淆网站到服务器 xxhelper.top
              uses: appleboy/scp-action@v0.1.4
              with:
                  host: ${{ matrix.server_ip }}
                  username: ${{ secrets.SERVER_USER }}
                  port: ${{ secrets.SSH_PORT }}
                  key: ${{ secrets.SSH_PRIVATE_KEY }}
                  # 前面加"/"就会被解析为“从 GitHub Actions 虚拟机的文件系统根目录开始找，通常找不到，不加代表从仓库根目录开始找
                  source: 'dist/*'
                  target: '/opt/1panel/1panel/www/sites/xxhelper.top/index'
                  overwrite: true
                  # 最终目录就是 target+(source-strip_components)，这里就是/etc/nginx/custom/
                  strip_components: 1