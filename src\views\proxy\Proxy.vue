<template>
    <div class="article-list">
        <!-- 自定义Loading组件 -->
        <Loading :visible="loading" :text="loadingText" :duration="loadingDuration" />

        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;代理管理</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-plus" size="medium" @click="showAddDialog">增加代理</el-button>
                <el-button type="primary" icon="el-icon-edit" size="medium" @click="showBatchEditDialog">批量修改</el-button>
                <el-button type="warning" icon="el-icon-refresh" size="medium" @click="updateExpiredStatus">更新状态</el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="multipleDelete" v-if="isAdmin">删除选中</el-button>
            </div>
            <div class="right">
                <el-input placeholder="请输入IP地址" v-model="ipSearch" clearable>
                    <el-button slot="append" icon="el-icon-search" @click="startSearch"></el-button>
                </el-input>
                <el-input placeholder="请输入端口" v-model="portSearch" clearable>
                    <el-button slot="append" icon="el-icon-search" @click="startSearch"></el-button>
                </el-input>
                <el-select v-model="stateSearch" placeholder="代理状态" clearable @change="startSearch">
                    <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button icon="el-icon-close" @click="clearSearch">清空</el-button>
            </div>
        </div>

        <!-- 内容栏 -->
        <el-table :data="proxyList" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="IP地址" prop="ip" align="center" sortable />
            <el-table-column label="端口" prop="port" width="100" align="center" sortable />
            <el-table-column label="用户名" prop="username"  align="center" />
            <el-table-column label="密码" prop="password"  align="center" />
            <el-table-column label="过期时间" prop="expiration_time" align="center" sortable>
                <template slot-scope="scope">
                    <span :style="{color: isExpired(scope.row.expiration_time) ? 'red' : 'black'}">
                        {{ formatDate(scope.row.expiration_time) }}
                    </span>
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="state" width="120" align="center" sortable>
                <template slot-scope="scope">
                    <el-tag :type="getStateType(scope.row.state)">{{ scope.row.state }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" width="150" align="center">
                <template slot-scope="scope">
                    <el-button type="text" size="small" @click="editProxy(scope.row)">编辑</el-button>
                    <el-button type="text" size="small" @click="deleteProxy(scope.row)" v-if="isAdmin">删除</el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 页码栏 -->
        <el-pagination class="pagination-container" :page-sizes="[10, 20, 50, 100]" :current-page="currentPage" 
            :page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper" 
            @size-change="sizeChange" @current-change="currentChange"></el-pagination>

        <!-- 弹窗：增加代理 -->
        <el-dialog :visible.sync="addDialogVisible" width="60%" top="3vh" title="批量增加代理">
            <div class="dialog-tip">
                请输入代理信息，每行一个，格式：IP:端口 用户名 密码 过期时间(YYYY-MM-DD)
            </div>
            <el-input placeholder="示例：*************:1890 fpyl01n140 VgXP74pI 2025-07-01" 
                type="textarea" :rows="10" v-model="proxyInputData"></el-input>
            <div slot="footer">
                <el-button @click="addDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="parseProxyData">格式化</el-button>
                <el-button type="primary" @click="addProxies" :disabled="!parsedProxyData.length">添 加</el-button>
            </div>
            <div v-if="proxyHtml" v-html="proxyHtml" class="parsed-data"></div>
        </el-dialog>

        <!-- 弹窗：批量修改 -->
        <el-dialog :visible.sync="batchEditDialogVisible" width="40%" top="5vh" title="批量修改代理">
            <el-form :model="batchEditForm" label-width="100px">
                <el-form-item label="过期时间">
                    <el-date-picker v-model="batchEditForm.expiration_time" type="date" 
                        placeholder="选择日期" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="batchEditForm.state" placeholder="请选择状态">
                        <el-option v-for="item in stateOptions" :key="item.value" 
                            :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="batchEditDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="batchUpdateProxies">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 弹窗：编辑单个代理 -->
        <el-dialog :visible.sync="editDialogVisible" width="40%" top="5vh" title="编辑代理">
            <el-form :model="editForm" label-width="100px">
                <el-form-item label="IP地址">
                    <el-input v-model="editForm.ip" disabled></el-input>
                </el-form-item>
                <el-form-item label="端口">
                    <el-input v-model="editForm.port" disabled></el-input>
                </el-form-item>
                <el-form-item label="用户名">
                    <el-input v-model="editForm.username"></el-input>
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="editForm.password"></el-input>
                </el-form-item>
                <el-form-item label="过期时间">
                    <el-date-picker v-model="editForm.expiration_time" type="date" 
                        placeholder="选择日期" value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="editForm.state" placeholder="请选择状态">
                        <el-option v-for="item in stateOptions" :key="item.value" 
                            :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer">
                <el-button @click="editDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="updateProxy">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import * as proxyApi from '@/api/proxyApi.js';
import Loading from '../component/Loading.vue';

export default {
    data() {
        return {
            // 权限
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',

            // 等待界面
            loading: false,
            loadingText: '正在处理中...',
            loadingDuration: 0,

            // 表格相关
            proxyList: [], // 代理列表数据
            multipleSelection: [], // 表格多选数据
            
            // 分页相关
            currentPage: 1,
            pageSize: 10,
            total: 0,

            // 搜索相关
            ipSearch: '',
            portSearch: '',
            stateSearch: '',
            stateOptions: [
                { value: 'idle', label: '空闲' },
                { value: 'active', label: '使用中' },
                { value: 'expired', label: '已过期' },
                { value: 'error', label: '异常' }
            ],

            // 增加代理弹窗
            addDialogVisible: false,
            proxyInputData: '',
            parsedProxyData: [],
            proxyHtml: '',

            // 批量修改弹窗
            batchEditDialogVisible: false,
            batchEditForm: {
                expiration_time: '',
                state: ''
            },

            // 编辑单个代理弹窗
            editDialogVisible: false,
            editForm: {
                id: null,
                ip: '',
                port: '',
                username: '',
                password: '',
                expiration_time: '',
                state: ''
            }
        };
    },
    methods: {
        // ========= 工具函数 =========
        // 格式化日期
        formatDate(date) {
            if (!date) return '';
            return date.split('T')[0];
        },

        // 判断是否过期
        isExpired(date) {
            if (!date) return false;
            return new Date(date) < new Date();
        },

        // 获取状态标签类型
        getStateType(state) {
            const map = {
                'idle': 'success',
                'active': 'primary',
                'expired': 'danger',
                'error': 'warning'
            };
            return map[state] || 'info';
        },

        // ========= 数据获取 =========
        // 获取代理列表
        async getProxyList() {
            try {
                const result = await proxyApi.getProxyList({
                    page: this.currentPage,
                    pageSize: this.pageSize,
                    ip: this.ipSearch.trim(),
                    port: this.portSearch.trim(),
                    state: this.stateSearch.trim()
                });
                this.proxyList = result.list || [];
                this.total = result.total || 0;
            } catch (error) {
                this.$message.error(`获取代理列表失败: ${error}`);
            }
        }, 

        // ========= 控制栏功能 =========
        // 显示增加代理弹窗
        showAddDialog() {
            this.addDialogVisible = true;
            this.proxyInputData = '';
            this.parsedProxyData = [];
            this.proxyHtml = '';
        },

        // 解析代理数据
        parseProxyData() {
            const lines = this.proxyInputData.trim().split('\n');
            const proxies = [];
            
            lines.forEach(line => {
                line = line.trim();
                if (!line) return;
                
                // 解析格式：IP:端口 用户名 密码 过期时间
                const parts = line.split(/\s+/);
                if (parts.length >= 4) {
                    const [ipPort, username, password, expiration_time] = parts;
                    const [ip, port] = ipPort.split(':');
                    
                    if (ip && port) {
                        proxies.push({
                            ip,
                            port,
                            username,
                            password,
                            expiration_time,
                            state: 'idle', // 默认状态
                            add_time: new Date()
                        });
                    }
                }
            });

            this.parsedProxyData = proxies;
            
            // 生成预览HTML
            if (proxies.length > 0) {
                let html = '<table border="1" cellspacing="0" cellpadding="5">';
                html += '<thead><tr><th>IP地址</th><th>端口</th><th>用户名</th><th>密码</th><th>过期时间</th><th>状态</th></tr></thead>';
                html += '<tbody>';
                proxies.forEach(proxy => {
                    html += '<tr>';
                    html += `<td>${proxy.ip}</td>`;
                    html += `<td>${proxy.port}</td>`;
                    html += `<td>${proxy.username}</td>`;
                    html += `<td>${proxy.password}</td>`;
                    html += `<td>${proxy.expiration_time}</td>`;
                    html += `<td>${proxy.state}</td>`;
                    html += '</tr>';
                });
                html += '</tbody></table>';
                this.proxyHtml = html;
            } else {
                this.proxyHtml = '<p style="color: red;">未能解析出有效的代理数据，请检查格式</p>';
            }
        },

        // 添加代理
        async addProxies() {
            if (this.parsedProxyData.length === 0) {
                this.$message.error('请先解析代理数据');
                return;
            }

            this.loading = true;
            this.loadingText = '正在添加代理...';
            this.loadingDuration = this.parsedProxyData.length * 500 + 2000;

            try {
                const result = await proxyApi.addProxy(this.parsedProxyData);
                this.$message.success(result.message || '添加成功');
                this.addDialogVisible = false;
                await this.getProxyList();
            } catch (error) {
                this.$message.error(`添加代理失败: ${error}`);
            } finally {
                this.loading = false;
            }
        },

        // 显示批量修改弹窗
        showBatchEditDialog() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要修改的代理');
                return;
            }
            this.batchEditDialogVisible = true;
            this.batchEditForm = {
                expiration_time: '',
                state: ''
            };
        },

        // 批量修改代理
        async batchUpdateProxies() {
            const updateData = [];
            this.multipleSelection.forEach(proxy => {
                const data = { id: proxy.id };
                if (this.batchEditForm.expiration_time) {
                    data.expiration_time = this.batchEditForm.expiration_time;
                }
                if (this.batchEditForm.state) {
                    data.state = this.batchEditForm.state;
                }
                updateData.push(data);
            });

            this.loading = true;
            this.loadingText = '正在批量修改...';
            this.loadingDuration = updateData.length * 500 + 2000;

            try {
                await proxyApi.batchUpdateProxy(updateData);
                this.$message.success('批量修改成功');
                this.batchEditDialogVisible = false;
                await this.getProxyList();
            } catch (error) {
                this.$message.error(`批量修改失败: ${error}`);
            } finally {
                this.loading = false;
            }
        },

        // 更新过期状态
        async updateExpiredStatus() {
            const expiredProxies = [];
            const now = new Date();
            
            // 找出选中的已过期代理
            this.multipleSelection.forEach(proxy => {
                if (proxy.expiration_time && new Date(proxy.expiration_time) < now && proxy.state !== 'expired') {
                    expiredProxies.push({
                        id: proxy.id,
                        state: 'expired'
                    });
                }
            });

            if (expiredProxies.length === 0) {
                this.$message.warning('没有需要更新状态的代理');
                return;
            }

            this.loading = true;
            this.loadingText = '正在更新状态...';
            this.loadingDuration = expiredProxies.length * 500 + 2000;

            try {
                await proxyApi.batchUpdateProxy(expiredProxies);
                this.$message.success(`成功更新 ${expiredProxies.length} 个代理的状态`);
                await this.getProxyList();
            } catch (error) {
                this.$message.error(`更新状态失败: ${error}`);
            } finally {
                this.loading = false;
            }
        },

        // 批量删除
        async multipleDelete() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要删除的代理');
                return;
            }

            try {
                await this.$confirm(`确定要删除选中的 ${this.multipleSelection.length} 个代理吗？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                this.loading = true;
                this.loadingText = '正在删除...';
                this.loadingDuration = this.multipleSelection.length * 500 + 2000;

                const ids = this.multipleSelection.map(item => item.id);
                const result = await proxyApi.delProxy(ids);
                this.$message.success('删除成功');
                await this.getProxyList();
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error(`删除失败: ${error}`);
                }
            } finally {
                this.loading = false;
            }
        },

        // 清空搜索
        clearSearch() {
            this.ipSearch = '';
            this.portSearch = '';
            this.stateSearch = '';
            this.currentPage = 1;
            this.getProxyList();
        },

        // 开始搜索
        startSearch() {
            this.currentPage = 1;
            this.getProxyList();
        },

        // ========= 表格操作 =========
        // 编辑单个代理
        editProxy(row) {
            this.editForm = { ...row };
            this.editDialogVisible = true;
        },

        // 更新单个代理
        async updateProxy() {
            try {
                await proxyApi.updateProxy(this.editForm);
                this.$message.success('更新成功');
                this.editDialogVisible = false;
                await this.getProxyList();
            } catch (error) {
                this.$message.error(`更新失败: ${error}`);
            }
        },

        // 删除单个代理
        async deleteProxy(row) {
            try {
                await this.$confirm(`确定要删除代理 ${row.ip}:${row.port} 吗？`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                await proxyApi.delProxy([row.id]);
                this.$message.success('删除成功');
                await this.getProxyList();
            } catch (error) {
                if (error !== 'cancel') {
                    this.$message.error(`删除失败: ${error}`);
                }
            }
        },

        // ========= 分页相关 =========
        // 改变每页显示数量
        sizeChange(size) {
            this.pageSize = size;
            this.currentPage = 1;
            this.getProxyList();
        },

        // 改变页码
        currentChange(current) {
            this.currentPage = current;
            this.getProxyList();
        }
    },
    created() {
        this.getProxyList();
    },
    components: {
        Loading
    }
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

.user-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
}

.control {
    overflow: hidden;
    margin-top: 20px;
    margin-bottom: 10px;

    .left {
        float: left;
    }

    .right {
        float: right;
        display: flex;
        width: 600px;
        justify-content: space-between;
        & > * {
            flex: 1 1 auto;
            margin-right: 5px;
        }
        .el-input {
            width: 200px;
        }
    }
}

.pagination-container {
    margin-top: 20px;
    text-align: right;
}

.dialog-tip {
    margin-bottom: 10px;
    color: #666;
    font-size: 14px;
}

.parsed-data {
    margin-top: 20px;
    max-height: 300px;
    overflow-y: auto;
}
</style>
