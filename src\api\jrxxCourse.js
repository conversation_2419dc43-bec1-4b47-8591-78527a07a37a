import axios from "@/utils/axios.js";
import apiPath from "../../config/apiPath";

let {jrxxCourseApiPath}=apiPath



//1.获取任务列表
export let getCourseList = (data) => axios.post(jrxxCourseApiPath.getCourseList, data);

//2.添加一个上课任务
export let addCourseTask = (data) => axios.post(jrxxCourseApiPath.addCourseTask, data);

//3.删除一个上课任务
export let delCourseTask = (data) => axios.post(jrxxCourseApiPath.delCourseTask, data);

//4.修改一个任务信息
export let updateCourseTask = (data) => axios.post(jrxxCourseApiPath.updateCourseTask, data);

//5.开始一个上课任务
export let runCourseTask = (data) => axios.post(jrxxCourseApiPath.runCourseTask, data);

//6.提交图片的base64编码
export let uploadImage = (data) => axios.post(jrxxCourseApiPath.uploadImage, data);

//7.获取用户头像的base64编码
export let getImage = (data) => axios.post(jrxxCourseApiPath.getImage, data);