# 更新日志

## 2025年7月18日更新
1. **页面** 
    - 优化Course和Exam页面样式布局
    - 修复Role的一个bug，上级默认权限下级


## 2025年7月17日更新
1. **页面** 
    - 增加proxy页面

## 2025年7月16日更新
1. **任务列队** 
    - 改名：task->queue
    - 增加删除功能
2. **网课考试** 
    - 重构开始，重置，删除三个逻辑

## 2月20日更新
1. **网课考试** 
    - 优化导出功能

## 2月19日更新
1. **登录** 
    - 更改前端获取机器码的逻辑，利用canvas+cpu+内存
2. **网课考试** 
    - 翻页增加等待动画
    - 重置任务修复了一个bug，之前前端API忘记写return返回数据了
    - 开始任务增加1秒延迟，用于更新列表数据


## 2月17日更新
1. **首页** 
    - 界面重写
    - 增加细节上的逻辑，例如登录状态提示
2. **网课作业页面** 
    - 增加，删除，开始任务重写API，批量操作的时候只发送一次请求


## 12月16日更新
1. **增加国开软件考试页面** 

## 12月12日更新
1. **修改重置任务逻辑** 
    - 尝试修复重置任务导致的内存溢出

## 11月7日更新
1. **分页不能设置500，否则导致内存溢出** 

## 11月5日更新
1. **10分钟后取消自动刷新，不然流量扛不住** 

## 10月29日更新
1. **增加任务列表页面** 

## 10月18日更新
1. **网课和作业增加导出excel表格功能** 

## 10月17日更新
1. **任务状态增加服务器中断和客户端中断** 

## 10月11日更新
1. **cluster进程** 
    - 增加一个resetCluster的api

## 10月10日更新
1. **添加代码混淆功能** 
    - 安装模块javascript-obfuscator
    - 增加配置文件obfuscator-config.json
    - 增加命令 npm run obfuscate
2. **任务增加了发起人** 
    - 

## 9月22日更新
5. **修复delCourseTask** 
    - 原来写法有问题，函数没有返回值

## 9月20日更新
1. **增加一个更新标记** 
    - 用于确保自动部署顺利完成，位置位于main.js
2. **修改dockerfile** 
    - 给 docker-entrypoint.sh 赋予执行权限
3. **修改compose.env.prod** 
    - 给注释前面添加#
4. **增加任务逻辑** 
    - normalizedData 支持'\'写法，会自动转为空字符串
    - multipleAddTask 增加了几个字段，保持和数据库结构匹配
5. **delCourseTask** 
    - 原来写法有问题，函数没有返回值



## 9月19日更新
1. **更改了环境变量的方式** 
    - 把nginx根据环境变量拆分了三个文件
    - 修改了dockerfile,增加了脚本文件
2. **修改了增加任务模块** 
    - 去掉了分隔符逻辑
    - 增加了一个html表格

## 9月2日更新
1. **修改nginx.conf** 
    - 修复了upload文件路径设置不正确导致的bug
2. **修改Exam.Vue** 
    - 上传照片用URL.createObjectURL(file.raw)立即显示

## 9月1日更新
1. **修改nginx.conf** 
    - 把expires配置放到了http块中统一设置，所有loaction块中的expires全部删除
    - 删除静态资源路由location ~* \.(js|css|png|jpg|jpeg|gif|ico)$   

## 8月30日更新 环境搭建完毕
1. **修改nginx.conf** 
    - 把root,index等从server块移动到location块
    - 增加一个server，解决开发和生产环境的问题
2. **修改docker-compose**
    - 把network_mode 改为了 1panel-network
    - 添加volumes，把主机的upload目录映射进来，用的环境变量写法
3. **修改Dockerfile**
    - 把创建目录命令提前，并且增加了两个目录index和upload
4. **修改增加.env.dev .env.prod文件，用于指定compose环境变量**
    - UPLOAD_PATH

## 8月28日更新
1. **修改nginx.conf** 
    - server_name从127.0.0.1改为了真实域名
2. **deploy.yml文件** ：通过github action实现自动化部署

## 8月28日更新
1. **添加Dcokerfile文件** ：用于制作镜像
1. **deploy.yml文件** ：通过github action实现自动化部署


## 8月26日更新
1. **初次建立项目** ：