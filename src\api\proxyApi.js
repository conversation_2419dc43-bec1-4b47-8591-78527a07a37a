import axios from '@/utils/axios.js';
import apiPath from '../../config/apiPath.js';

let { proxyApiPath } = apiPath;

// 获取代理列表
export const getProxyList = data => axios.get(proxyApiPath.getProxyList, {
    params: data
});


// 批量添加代理
export const addProxy = proxyList => axios.post(proxyApiPath.addProxy, proxyList);


// 批量删除代理
export const delProxy = idList => axios.delete(proxyApiPath.delProxy, {
    data: idList
});


// 修改代理信息
export const updateProxy = proxyData => axios.put(proxyApiPath.updateProxy, proxyData);


// 批量修改代理信息
export const batchUpdateProxy = proxyList => {
    const promises = proxyList.map(proxy => updateProxy(proxy));
    return Promise.all(promises);
} 