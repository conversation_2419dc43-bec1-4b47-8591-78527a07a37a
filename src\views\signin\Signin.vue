<template>
    <div class="login-container">
        <!-- 背景装饰元素 -->
        <div class="login-background">
            <div class="login-shape"></div>
            <div class="login-shape"></div>
        </div>

        <!-- 登录卡片主体 -->
        <div class="login-card">
            <!-- 左侧品牌区域 -->
            <div class="login-left">
                <div class="brand-content">
                    <div class="logo">
                        <i class="el-icon-s-platform"></i>
                    </div>
                    <h2 class="system-name">学习助手</h2>
                    <p class="system-slogan">高效、安全的一站式教务平台</p>
                </div>
            </div>

            <!-- 右侧登录表单区域 -->
            <div class="login-right">
                <el-form :model="formData" :rules="rules" ref="signinForm" class="login-form">
                    <h3 class="welcome-title">欢迎登录</h3>
                    <p class="welcome-subtitle">请使用您的管理员账号登录系统</p>

                    <!-- 邮箱输入框 -->
                    <el-form-item prop="email" class="custom-form-item">
                        <el-input prefix-icon="el-icon-message" placeholder="请输入邮箱" v-model="formData.email">
                        </el-input>
                    </el-form-item>

                    <!-- 密码输入框 -->
                    <el-form-item prop="password" class="custom-form-item">
                        <el-input prefix-icon="el-icon-lock" placeholder="请输入密码" type="password" v-model="formData.password">
                        </el-input>
                    </el-form-item>

                    <!-- 表单底部功能区 -->
                    <div class="form-footer">
                        <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                        <el-link type="primary" class="forgot-password">忘记密码？</el-link>
                    </div>

                    <!-- 登录按钮 -->
                    <el-button type="primary" class="login-button" @click="submitForm">
                        登录
                        <i class="el-icon-right"></i>
                    </el-button>
                </el-form>

                <!-- 页脚版权信息 -->
                <div class="login-footer">
                    <p>© 2025 学习助手 - 保留所有权利</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions } from 'vuex';
import md5 from 'blueimp-md5';
export default {
    data() {
        return {
            // 表单数据
            formData: {
                email: '',
                password: ''
            },
            // 记住我选项
            rememberMe: false,
            // 表单验证规则
            rules: {
                email: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' },
                    { min: 6, max: 50, message: '邮箱长度需在6到50个字符之间', trigger: 'change' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, max: 20, message: '密码长度需在6到20个字符之间', trigger: 'change' }
                ],
            }
        }
    },
    methods: {
        // 从Vuex导入用户登录和路由添加actions
        ...mapActions('user', ['user_signin', 'add_route']),

        // 表单提交方法
        async submitForm() {
            try {

                let hardwareConcurrency = navigator.hardwareConcurrency || 'unknown'
                let deviceMemory = navigator.deviceMemory || 'unknown'
                let pcId = md5(`${navigator.userAgent}-${window.screen.width}-${window.screen.height}-${hardwareConcurrency}-${deviceMemory}`);
                console.log(pcId)


                // 表单验证
                let valid = await this.$refs.signinForm.validate().catch(err => {
                    return false;
                });

                if (!valid) {
                    this.$message.error('表单验证失败，请检查输入');
                    return;
                }
                this.formData.pcId = pcId;
                // 调用用户登录action
                await this.user_signin(this.formData);

                // 登录成功提示
                this.$message({
                    message: `登录成功！欢迎回来：${this.$store.state.user.userInfo.user_email}`,
                    type: 'success',
                    duration: 2000
                });

                // 延迟1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 添加路由
                await this.add_route(this.$store.state.user.userInfo);

                // 跳转到首页
                this.$router.push({ path: '/' });
            } catch (error) {
                // 登录失败处理
                this.$message.error(`登录失败，${error}`);
            }
        }
    }
}
</script>

<style scoped lang="scss">
/* 登录页面容器 */
.login-container {
    position: relative;
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(
        135deg,
        #667eea 0%,
        #764ba2 100%
    ); /* 渐变背景 */
    overflow: hidden;
}

/* 背景装饰 */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    /* 圆形装饰元素 */
    .login-shape {
        position: absolute;
        border-radius: 50%;

        /* 右上角大圆形 */
        &:first-child {
            background: rgba(255, 255, 255, 0.1);
            width: 500px;
            height: 500px;
            top: -250px;
            right: -100px;
        }

        /* 左下角小圆形 */
        &:last-child {
            background: rgba(255, 255, 255, 0.1);
            width: 300px;
            height: 300px;
            bottom: -150px;
            left: -50px;
        }
    }
}

/* 登录卡片主体 */
.login-card {
    z-index: 2;
    display: flex;
    width: 900px;
    height: 560px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    background-color: #fff;

    /* 左侧品牌区域 */
    .login-left {
        width: 40%;
        background-image: linear-gradient(
            135deg,
            #5ee7df 0%,
            #b490ca 100%
        ); /* 渐变背景 */
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;

        /* 品牌内容 */
        .brand-content {
            text-align: center;
            color: white;

            /* Logo样式 */
            .logo {
                font-size: 60px;
                margin-bottom: 20px;

                i {
                    padding: 20px;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 50%;
                }
            }

            /* 系统名称 */
            .system-name {
                font-size: 28px;
                margin-bottom: 15px;
                font-weight: 600;
            }

            /* 系统标语 */
            .system-slogan {
                opacity: 0.9;
                font-size: 16px;
                letter-spacing: 1px;
            }
        }
    }

    /* 右侧登录表单区域 */
    .login-right {
        width: 60%;
        padding: 50px 60px;
        display: flex;
        flex-direction: column;

        /* 欢迎标题 */
        .welcome-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        /* 欢迎副标题 */
        .welcome-subtitle {
            font-size: 14px;
            color: #999;
            margin-bottom: 40px;
        }

        /* 登录表单 */
        .login-form {
            flex: 1;

            /* 表单项 */
            .custom-form-item {
                margin-bottom: 25px;
            }

            /* 输入框样式 */
            ::v-deep .el-input__inner {
                height: 50px;
                border-radius: 8px;
                border: 1px solid #e0e0e0;
                transition: all 0.3s;
                padding-left: 40px; /* 增加左内边距，使图标和输入内容有足够间距 */

                /* 输入框聚焦效果 */
                &:focus {
                    border-color: #764ba2;
                    box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.2);
                }
            }

            /* 输入框图标前缀 */
            ::v-deep .el-input__prefix {
                left: 12px;
                color: #999;
            }

            /* 表单底部功能区 */
            .form-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 30px;

                /* 忘记密码链接 */
                .forgot-password {
                    font-size: 14px;
                }
            }

            /* 登录按钮 */
            .login-button {
                width: 100%;
                height: 50px;
                border-radius: 8px;
                font-size: 16px;
                letter-spacing: 1px;
                background: linear-gradient(
                    135deg,
                    #667eea 0%,
                    #764ba2 100%
                ); /* 渐变背景 */
                border: none;
                transition: all 0.3s;

                /* 按钮悬浮效果 */
                &:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 5px 15px rgba(118, 75, 162, 0.3);
                }
            }
        }

        /* 页脚版权信息 */
        .login-footer {
            text-align: center;
            font-size: 12px;
            color: #999;
            margin-top: 20px;
        }
    }
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
    .login-card {
        width: 90%;
        height: auto;
        flex-direction: column;

        /* 响应式左侧区域 */
        .login-left {
            width: 100%;
            padding: 30px;

            .brand-content {
                .logo {
                    font-size: 40px;
                    margin-bottom: 10px;
                }

                .system-name {
                    font-size: 22px;
                }
            }
        }

        /* 响应式右侧区域 */
        .login-right {
            width: 100%;
            padding: 30px;
        }
    }
}
</style>