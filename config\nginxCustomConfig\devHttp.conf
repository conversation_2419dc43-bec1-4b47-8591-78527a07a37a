# 开发环境，直接用vscode启动
server {
    # web服务器设置
    listen 80 ; # 监听端口
    server_name dev.localhost; # 服务器名称，可以是域名或 IP 地址。


    # 日志配置
    access_log /www/sites/courseWeb/log/access.log; # 访问日志文件路径及日志格式
    error_log /www/sites/courseWeb/log/error.log; # 错误日志文件路径 error日志不接受日志级别


    # 路由配置：反向代理配置
    location ^~/api/ {
        # 容器不能通过127.0.0.1访问主机，可以使用host.docker.internal
        # host.docker.internal 是在 macOS 和 Windows 中 Docker 的默认名称，用来指向宿主机。要在 Linux 中启用它，你需要在 Docker 运行时手动定义这个主机名。
        # 可以直接写如linunx的Host echo "************* host.docker.internal" >> /etc/hosts
        proxy_pass http://host.docker.internal:2000;
        proxy_http_version 1.1; # 设置 HTTP 协议版本为 1.1

        # 保留客户端请求的 Host、IP等信息，传递给后端
        proxy_set_header Host $host; # 原始客户端请求的 Host(域名)
        proxy_set_header X-Real-IP $remote_addr; # 客户端的真实 IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 代理链上的所有 IP
        proxy_set_header X-Forwarded-Host $host; # 也可用 $server_name，看后端如何解析

        # 网站设置
        # error_page 502 /backend_down.html; # 当发生 502 错误时返回 backend_down.html 页
    }

    # history模式 try_files指令，用来尝试按顺序找到第一个存在的文件，并返回它。
    # $uri：这是Nginx的一个内置变量，表示请求的URI，
    location / {
        root /www/sites/courseWeb/dist; # 网站资源根目录
        try_files $uri $uri/ /index.html;
        index index.php index.html index.htm default.php default.htm default.html; # 默认的索引文件列表
        # error_page 404 /404.html; # 自定义 404 错误页面

        proxy_cache off;
        proxy_no_cache 1;
        proxy_cache_bypass 1;

    }

    # root 会把url中的path和root配置拼接起来
    # alias 等于url中的path减去loction配置，然后加上alias配置
    location ^~ /avatar/ {
        root /www/sites/courseWeb/upload; # 末尾不能加 /
    }
}