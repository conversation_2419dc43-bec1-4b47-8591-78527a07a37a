<template>
    <div class="signinWrap">
        <aside>
        </aside>
        <main>
            <el-form :model="formData" :rules="rules" ref="signinForm"> <!--data.attrs:{formData:formData}  -->
                <h3>管理员登录</h3>
                <el-form-item prop="email">
                    <el-input placeholder="请输入邮箱" v-model="formData.email"></el-input>
                    <!--@input="e=>formData.email=e.target.value"-->
                </el-form-item>
                <el-form-item prop="password">
                    <el-input placeholder="请输入密码" type="password" v-model="formData.password"></el-input>
                </el-form-item>
                <el-button type="primary" @click="submitForm">登录</el-button>
            </el-form>
        </main>
    </div>
</template>

<script>
import { mapActions } from 'vuex';
export default {
    data() {
        return {
            formData: {
                email: '',
                password: ''
            },
            rules: {
                email: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { min: 6, max: 20, message: '长度3到20个字符', trigger: 'change' }
                ],
                password: [
                    { required: true, message: '请输入邮箱', trigger: 'blur' },
                    { min: 6, max: 20, message: '长度3到20个字符', trigger: 'change' }
                ],
            }
        }
    },
    methods: {
        ...mapActions('user', ['user_signin', 'add_route']),
        submitForm() {
            this.$refs['signinForm'].validate(async isValidated => {
                if (isValidated) {
                    //用户登录
                    try {
                        await this.user_signin(this.formData)
                        this.$message.success(`登录成功！欢迎回来：${this.$store.state.user.userInfo.user_email}`);
                        await new Promise(resolve => setTimeout(resolve, 1000))
                        //动态获取路由
                        await this.add_route(this.$store.state.user.userInfo)
                        //路由跳转
                        this.$router.push({ path: '/' })
                    } catch (error) {
                        this.$message.error(`登录失败，${error}`);
                    }

                } else {
                    this.$message.error(`用户名/密码格式错误，请重新输入`);
                    return false
                }
            })
        }
    }
}
</script>

<style scoped lang="scss">
.signinWrap {
    overflow: hidden;

    aside {
        background-color: pink;
        width: calc(100% - 480px);
        height: 100vh;
        float: left;
    }

    main {
        float: right;
        width: 480px;
        height: 100vh;
        justify-content: center;
        align-items: center;
        padding: 0 50px;
        padding-top: 100px;

        h3 {
            text-align: center;
        }

        button {
            width: 100%;
        }
    }
}
</style>
