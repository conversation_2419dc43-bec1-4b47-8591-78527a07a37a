<template>
    <div class="article-list">
        <!-- 数据更新状态指示器 -->
        <div class="update-status-bar" v-if="isUpdatePaused">
            <i class="el-icon-warning"></i>
            <span>数据更新已暂停</span>
            <el-button type="text" size="mini" @click="resumeUpdate">立即恢复</el-button>
        </div>

        <!-- 自定义Loading组件 -->
        <Loading :visible="loading" :text="loadingText" :duration="loadingDuration" />

        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;{{meta.name}}</div>

        <!-- 控制栏 -->
        <div class="control">
            <!-- 搜索区域 -->
            <div class="search-section">
                <el-input placeholder="请输入用户名" v-model="usernameSearch" clearable size="medium" @keyup.enter="startSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-input placeholder="请输入备注" v-model="commentSearch" clearable size="medium" @keyup.enter="startSearch">
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
                <el-select v-model="stateSearch" placeholder="任务状态" clearable size="medium" @change="startSearch">
                    <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-button icon="el-icon-search" size="medium" @click="startSearch">
                    查询
                </el-button>
                <el-button icon="el-icon-refresh-left" size="medium" @click="clearSearch">
                    重置
                </el-button>
            </div>

            <!-- 操作按钮区域 -->
            <div class="action-section">
                <!-- 任务操作组 -->
                <div class="button-group">
                    <el-button type="primary" icon="el-icon-plus" size="medium" @click="addOrEdit = true">
                        添加任务
                    </el-button>
                    <el-button type="success" icon="el-icon-video-play" size="medium" @click="multipleRunTask">
                        开始选中
                    </el-button>
                    <el-button type='warning' icon="el-icon-refresh" size="medium" @click="resetTask">
                        重置任务
                    </el-button>
                    <el-button type="danger" icon="el-icon-delete" size="medium" @click="multipleDelete('')" v-if="isAdmin">
                        删除选中
                    </el-button>
                    <el-button type="danger" icon="el-icon-circle-close" size="medium" @click="multipleDelete('失败')" v-if="false">
                        删除失败
                    </el-button>
                    <download-excel :fields="json_fields" name="examResult.xls" :stringifyLongNum="true" :fetch="fetchData">
                        <el-button type="primary" plain icon="el-icon-download" size="medium">
                            导出数据
                        </el-button>
                    </download-excel>
                </div>

                <!-- 数据操作组 -->
                <!-- <div class="button-group">
                    <div class="switch-container">
                        <el-switch v-model="autoRefresh" active-text="自动刷新" inactive-text="手动刷新" active-color="#409EFF" inactive-color="#C0C4CC" @change="handleRefreshChange">
                        </el-switch>
                    </div>
                </div> -->
            </div>
        </div>

        <!-- 内容栏 -->
        <el-table ref="examTable" :data="taskList" border @selection-change="val => (multipleSelection = val)">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="用户" width="220" align="center">
                <span slot-scope="scope">用户 {{scope.row.username}} <br /> 密码 {{scope.row.password}}</span>
            </el-table-column>
            <el-table-column v-if="meta.hasCourseTerm" label="学期" width="50" prop="term" align="center" />
            <el-table-column v-if="meta.hasCourseName" label="课程" width="120" prop="coursename" align="center" />
            <el-table-column v-if="meta.hasPhotoColumn" label="照片" width="100" align="center">
                <template slot-scope='scope'>
                    <el-upload class="avatar-uploader" :show-file-list="false" :before-upload="beforeAvatarUpload" :http-request="(options) => uploadToGallery(options.file, scope.row)" action="#" :data=scope.row name='avatar'>
                        <img v-if="scope.row.imageUrl" :src="scope.row.imageUrl" class="avatar image">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                </template>
            </el-table-column>
            <el-table-column label="状态" prop="state" width="80" align="center" sortable />
            <el-table-column label="日志" align="center">
                <template slot-scope="scope">
                    <div class="logger" @click='openLogger(scope.row)'>
                        <div v-html="scope.row.logMessage"></div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="进度" width="300" align="center">
                <template slot-scope="scope">
                    <div v-html="scope.row.progressMsg"></div>
                </template>
            </el-table-column>
            <el-table-column label="错误" width="300" align="center">
                <template slot-scope="scope">
                    <div class="logger" @click='openErrorLogger(scope.row.error_info)'>
                        <div v-html="scope.row.error_info"></div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column label="备注" width="80" prop="comment" align="center" sortable />
        </el-table>

        <!-- 页码栏 -->
        <el-pagination class="pagination-container" :page-sizes="[10, 20, 50,100]" :current-page="currentPage" :page-size="pageSize" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="sizeChange" @current-change="currentChange"></el-pagination>

        <!-- 弹窗，批量添加 用户名，密码，学校名 -->
        <el-dialog :visible.sync="addOrEdit" width="60%" top="3vh" title="批量添加">
            <el-input placeholder="请输入备注" v-model="comment"></el-input>
            <el-input placeholder="[username, password, schoolname, year, term, coursename] " type="textarea" :rows="5" v-model="courseData"></el-input>
            <div slot="footer">
                <el-button @click="addOrEdit = false">取 消</el-button>
                <el-button type="primary" @click="normalizedData">格式化</el-button>
                <el-button type="primary" @click="multipleAddTask" :disabled="buttonDisabled">添 加</el-button>
            </div>
            <div v-html="courseHtml"></div>
        </el-dialog>

        <!-- 弹窗，操作日志 -->
        <el-dialog :visible.sync="showLoginfoDialogVisible" width="30%" top="5vh" title="详细日志">
            <div v-html="loginfo"></div>
        </el-dialog>

        <!-- 弹窗，错误日志 -->
        <el-dialog :visible.sync="showErrorDialogVisible" width="30%" top="5vh" title="详细日志">
            <div v-html="loginfo"></div>
        </el-dialog>
    </div>
</template>

<script>

import * as examApi from '@/api/examApi.js'
import * as clusterApi from '@/api/clusterApi.js';
import * as clientApi from '@/api/clientApi.js';
import { v4 } from 'uuid';
import JsonExcel from 'vue-json-excel'
import Loading from '../component/Loading.vue'
export default {
    data() {
        return {
            //网站数据
            meta: {
                platform: '',
                name: '',
                hasPhotoColumn: false, //是否有人脸识别的列
                photoWidth: 100, // 照片宽高比
                photoHeight: 100, // 照片宽高比
                photoExt: 'image/jpeg', // 图片格式
                hasCourseTerm: false, // 课程学起
                hasCourseName: false, // 学期
            },
            //底部页码
            currentPage: 1, //当前页码
            pageSize: 10, //每页显示数量
            total: 0, //总的数量

            // 权限
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',

            // 表格相关
            taskList: [], //表格数据
            multipleSelection: [], //表格多选
            // imageUrl: '',

            //批量添加弹窗


            autoRefresh: true, //自动刷新页面

            // 用户活动检测相关
            lastActivityTime: Date.now(), // 最后活动时间
            activityCheckTimer: null,     // 活动检测定时器
            isUpdatePaused: false,         // 是否已暂停更新
            IDLE_TIME_LIMIT: 3 * 60 * 1000, // 空闲时间限制：3分钟
            refreshTimer: null,            // 刷新定时器

            // ========= 添加任务弹窗 =========
            addOrEdit: false, //批量添加数据弹窗开关
            courseData: '', //批量添加数据
            courseHtml: '',
            buttonDisabled: true, //添加按钮是否禁用
            comment: '', //备注

            // ========= 日志弹窗 =========
            showLoginfoDialogVisible: false, //修改用户信息弹窗开关
            loginfo: '',//日志数据

            //错误信息弹窗
            showErrorDialogVisible: false, //修改用户信息弹窗开关
            errorinfo: '',//错误日志数据


            // ========= 控制栏 =========
            commentSearch: '',//搜索时候的备注
            usernameSearch: '', //用户输入的搜索内容
            stateOptions: [
                {
                    value: '等待',
                    label: '等待'
                },
                {
                    value: '正在进行',
                    label: '正在进行'
                },
                {
                    value: '完成',
                    label: '完成'
                },
                {
                    value: '列队中',
                    label: '列队中'
                },
                {
                    value: '失败',
                    label: '失败'
                },
                {
                    value: '服务器中断',
                    label: '服务器中断'
                }, {
                    value: '客户端中断',
                    label: '客户端中断'
                },
            ],
            stateSearch: '',

            // 导出数据为excel表格
            // 表格数据
            excelpage: [],
            // 表格表头
            json_fields: {
                用户名: { field: "username" },
                密码: { field: "password", callback: (value) => `="${value}"` },
                学校: { field: "schoolname" },
                平台: { field: "platform" },
                状态: { field: "state" },
                科目: { field: "subject" },
                分数: { field: "score" },
                备注: { field: "comment" },
                最后一条日志: { field: "logMessage" },
            },
            json_meta: [
                [
                    {
                        " key ": " charset ",
                        " value ": " utf- 8 "
                    }
                ]
            ],

            // 等待界面
            loading: false,
            loadingText: '正在进行任务中...',
            loadingDuration: 0
        };
    },
    methods: {
        // ========= 控制栏 =========
        // 处理刷新开关变化
        handleRefreshChange(value) {
            this.autoRefresh = value;

            // 如果开启自动刷新
            if (value) {
                // 如果没有被暂停，立即开始刷新
                if (!this.isUpdatePaused) {
                    this.updateTaskList();
                    // 重置活动时间
                    this.recordUserActivity();
                    // 设置定时器
                    if (this.refreshTimer) {
                        clearInterval(this.refreshTimer);
                    }
                    this.refreshTimer = setInterval(() => {
                        this.updateTaskList();
                    }, 2000);
                }
            } else {
                // 如果关闭自动刷新，清除定时器
                if (this.refreshTimer) {
                    clearInterval(this.refreshTimer);
                    this.refreshTimer = null;
                }
                // 重置暂停状态
                this.isUpdatePaused = false;
            }
        },

        //批量删除
        async multipleDelete(filterState) {
            let selection;
            // 传入了删除状态
            if (filterState) {
                // 如果传入了状态参数，则删除所有该状态的任务
                selection = this.taskList.filter(item => item.state === filterState);
                if (selection.length === 0) {
                    this.$message.error(`没有找到状态为"${filterState}"的数据`);
                    return;
                }
            }
            // 没有传入删除状态，则删除选中的任务
            if (!filterState) {
                // 否则删除选中的任务
                selection = this.multipleSelection;
                if (selection.length === 0) {
                    this.$message.error('请先选择要删除的数据');
                    return;
                }
            }

            const originalLength = selection.length;
            this.loading = true;
            this.loadingText = '正在删除任务...';
            this.loadingDuration = originalLength * 600 + 2000;

            try {
                // 把任务分为三种情况
                const runningTasks = selection.filter(item => item.state === '正在进行');
                const queuedTasks = selection.filter(item => item.state === '列队中');

                // 1. 处理“正在进行”的任务
                if (runningTasks.length > 0) {
                    const runningTaskIds = runningTasks.map(item => ({ id: item.id }));
                    const runningClusterInfoList = await clusterApi.getClusterInfoByTaskId(runningTaskIds);
                    if (runningClusterInfoList.length > 0) {
                        await clusterApi.resetCluster(runningClusterInfoList);
                    }
                }

                // 2. 处理“列队中”的任务
                if (queuedTasks.length > 0) {
                    const queuedTasksByPcId = queuedTasks.reduce((acc, task) => {
                        if (task.pc_id) {
                            if (!acc[task.pc_id]) {
                                acc[task.pc_id] = [];
                            }
                            acc[task.pc_id].push({ id: task.id });
                        }
                        return acc;
                    }, {});

                    for (const pcId in queuedTasksByPcId) {
                        await clientApi.delClientQueue({
                            pcId: pcId,
                            queueList: queuedTasksByPcId[pcId],
                        });
                    }
                }

                // 3. 统一删除所有选中的任务
                const tasksToDelete = selection.map(item => ({ id: item.id }));
                if (tasksToDelete.length > 0) {
                    const result = await examApi.delExamTask(tasksToDelete);
                    const deleteMessage = filterState ? `成功删除 ${result.delCount} 条状态为"${filterState}"的数据` : `成功删除 ${result.delCount} 条数据`;
                    this.$message.success(deleteMessage);
                }

            } catch (error) {
                this.$message.error(`删除任务时出错: ${error}`);
            } finally {
                await this.updateTaskList();
                this.loading = false;
            }
        },
        //批量增加任务
        async multipleAddTask() {
            this.buttonDisabled = true; // 启用"添加"按钮
            let courseArr = JSON.parse(this.courseData);
            if (courseArr.length === 0) {
                this.$message.error('请先选择数据')
                return
            }
            courseArr = courseArr.map(item => {
                return {
                    id: v4(),
                    username: item.username,
                    password: item.password,
                    schoolname: item.schoolname,
                    platform: this.meta.platform,
                    year: item.year,
                    term: item.term,
                    coursename: item.coursename,
                    others: item.others,
                    // schoolurl: '', // 由服务器根据schoolname自动填充
                    state: '等待',
                    add_time: new Date(),
                    // start_time: '', // 由服务器自动填充
                    // end_time: '', // 由服务器自动填充
                    // error_info: '', // 由服务器自动填充
                    // final_result: '', // 由服务器自动填充
                    comment: this.comment,
                    user: this.$store.state.user.userInfo.user_email, //任务发起人
                }
            })

            try {
                this.loading = true;
                this.loadingText = '正在添加任务...';
                this.loadingDuration = courseArr.length * 500 + 2000; // 每个任务预计0.5秒
                let result = await examApi.addExamTask(courseArr);
                // 重置添加任务弹窗的状态
                this.addOrEdit = false; // 关闭弹窗
                this.courseData = ''; // 清空数据
                this.comment = ''; // 清空备注
                this.courseHtml = ''; // 清空html
                await this.updateTaskList()
                this.loading = false;
                this.$message.success(`成功添加${result.totalCount}条数据，其中新增${result.createCount}条数据，更新${result.updateCount}条数据`);
            } catch (error) {
                this.loading = false;
                this.$message.error(error);
            }
        },
        //批量开始任务
        async multipleRunTask() {

            // 只有状态为“等待”的才能开始
            this.multipleSelection = this.multipleSelection.filter(item => item.state == '等待')

            let length = this.multipleSelection.length;
            if (length === 0) {
                this.$message.error('请先选择数据')
                return
            }
            this.loading = true
            this.loadingText = '正在启动任务...';
            this.loadingDuration = this.multipleSelection.length * 600 + 2000; // 每个任务预计0.6秒
            let examTaskArr = this.multipleSelection.map(item => { return { ...item, runCourse: true, runAssignment: true } })
            try {
                let runRes = await examApi.runExamTask(examTaskArr);

                await this.updateTaskList()

                await new Promise(r => setTimeout(r, 500))
                await this.updateTaskList()

                this.loading = false

                this.$message.success(`成功开始${length}条数据，请手动开启进度更新，其中${runRes.runCount}条数据开始运行，${runRes.addCount}条数据加入列队`);
            } catch (error) {
                this.loading = false
                this.$message.error(`任务开始失败：${error} `);
            }
        },

        //清空搜索内容
        async clearSearch() {
            this.usernameSearch = '';
            this.commentSearch = '';
            this.stateSearch = '';
            await this.updateTaskList()
        },
        //搜索
        async startSearch() {
            this.currentPage = 1
            await this.updateTaskList()
        },
        //重置任务
        async resetTask() {
            let length = this.multipleSelection.length;
            if (length === 0) {
                this.$message.error('请先选择数据')
                return
            }

            // 开启loading
            this.loading = true
            this.loadingText = '正在重置任务...';
            this.loadingDuration = this.multipleSelection.length * 500 + 2000; // 每个任务预计0.5秒

            // 已经是等待的就不需要重置了
            this.multipleSelection = this.multipleSelection.filter(item => item.state != '等待')

            // 把任务分为三种情况
            const runningTasks = this.multipleSelection.filter(item => item.state === '正在进行');
            const queuedTasks = this.multipleSelection.filter(item => item.state === '列队中');
            const otherTasks = this.multipleSelection.filter(item => item.state !== '正在进行' && item.state !== '列队中');

            // 需要重置状态的任务列表
            let tasksToResetState = [];

            // 1. 处理“正在进行”的任务：获取进程信息 -> 服务器cluster重置任务-> 客户端重置进程 
            if (runningTasks.length > 0) {
                let runningTaskIds = runningTasks.map(item => ({ id: item.id }));
                try {
                    // 获取任务对应的进程信息，有的任务没有进程数据，就不会被返回
                    let runningClusterInfoList = await clusterApi.getClusterInfoByTaskId(runningTaskIds);
                    // 对有进程信息的任务，调用接口重置
                    if (runningClusterInfoList.length > 0) {
                        await clusterApi.resetCluster(runningClusterInfoList);
                    }

                    // 找出没有对应进程信息的“正在进行”任务，直接加入状态重置列表
                    let foundClusterTaskIds = new Set(runningClusterInfoList.map(info => info.task_id));
                    runningTasks.forEach(task => {
                        if (!foundClusterTaskIds.has(task.id)) {
                            tasksToResetState.push(task);
                        }
                    });
                } catch (error) {
                    this.$message.error(`重置"正在进行"任务失败: ${error}`);
                }
            }

            // 2. 处理“列队中”的任务：客户端负责删除列队和重置任务，服务器只负责传达消息
            if (queuedTasks.length > 0) {
                // 按 pc_id 分组
                const queuedTasksByPcId = queuedTasks.reduce((acc, task) => {
                    if (task.pc_id) {
                        if (!acc[task.pc_id]) {
                            acc[task.pc_id] = [];
                        }
                        acc[task.pc_id].push({ id: task.id });
                    } else {
                        // 没有 pc_id 的列队中任务，直接加入状态重置列表
                        tasksToResetState.push(task);
                    }
                    return acc;
                }, {});

                try {
                    // 为每个客户端删除队列中的任务
                    for (const pcId in queuedTasksByPcId) {
                        await clientApi.delClientQueue({
                            pcId: pcId,
                            queueList: queuedTasksByPcId[pcId],
                        });
                    }
                } catch (error) {
                    this.$message.error(`从任务队列中删除失败: ${error}`);
                }
            }

            // 3. 处理其他状态的任务，直接加入状态重置列表
            tasksToResetState.push(...otherTasks);

            // 4. 统一更新需要重置状态的任务
            if (tasksToResetState.length > 0) {
                const finalTasksToUpdate = tasksToResetState.map(item => ({
                    id: item.id,
                    state: "等待",
                    error_info: "",
                    final_result: '',
                    pc_id: '',
                }));

                try {
                    await examApi.updateExamTask(finalTasksToUpdate);
                    await examApi.clearExamLog(finalTasksToUpdate);
                } catch (error) {
                    this.$message.error(`更新任务状态失败: ${error}`);
                }
            }

            // 更新列表并关闭 loading
            await new Promise(r => setTimeout(r, 1000))
            await this.updateTaskList();
            this.loading = false;
            this.$message.success(`重置任务操作已发送，共处理${length}条数据`);
        },
        //导出数据为excel表格
        async fetchData() {
            //从服务器获取数据
            let taskListResult = await examApi.getExamList({
                currentPage: 1, //当且页码
                pageSize: 2000, //每页显示多少
                searchData: {
                    username: this.usernameSearch.trim(),
                    comment: this.commentSearch.trim(),
                    state: this.stateSearch.trim(),
                },
                platform: this.meta.platform,
                host: location.host,
            });

            let taskList = taskListResult.taskList;
            // 2. 遍历后端返回的列表，根据每个 item 的 final_result 拆分多行
            let excelpage = [];
            taskList.forEach(item => {
                if (item.exam_info_logs.length == 0) {
                    item.logMessage = ''
                } else {
                    item.logMessage = item.exam_info_logs[0]
                }
                // 如果 final_result 是一个数组，就逐项拆分
                if (Array.isArray(item.final_result)) {
                    item.final_result.forEach(course => {
                        excelpage.push({
                            username: item.username,
                            password: item.password,
                            schoolname: item.schoolname,
                            platform: item.platform,
                            state: item.state,
                            subject: course.courseName,
                            score: course.progress,
                            comment: item.comment,
                            logMessage: item.logMessage + ''
                        });
                    });
                } else {
                    // 如果没有 final_result 数组，就保持单行（或根据需求自行处理）
                    excelpage.push({
                        username: item.username,
                        password: item.password,
                        schoolname: item.schoolname,
                        platform: item.platform,
                        state: item.state,
                        subject: '',
                        score: '',
                        comment: item.comment,
                        logMessage: item.logMessage + ''
                    });
                }
            });

            // 3. 将最终整理好的 excelpage 返回给 vue-json-excel
            return excelpage
        },

        // ========= 表格内容栏 =========
        //更新表格数据
        async updateTaskList() {
            // 1. 保存当前选中的行的ID
            const selectedIds = new Set(this.multipleSelection.map(item => item.id));

            //从服务器获取数据
            let taskListResult = await examApi.getExamList({
                currentPage: this.currentPage, //当且页码
                pageSize: this.pageSize, //每页显示多少
                searchData: {
                    username: this.usernameSearch.trim(),
                    comment: this.commentSearch.trim(),
                    state: this.stateSearch.trim(),
                },
                platform: this.meta.platform,
                host: location.host,
            });

            let taskList = taskListResult.taskList;
            for (let item of taskList) {
                //日志
                item.logMessage = ''
                item.exam_info_logs.forEach(log => {
                    item.logMessage += log
                })

                // 进度信息
                item.progressMsg = ''
                if (Array.isArray(item.final_result)) {
                    item.final_result.forEach(course => {
                        let color = course.progress >= this.meta.passProgress ? 'black' : 'red'
                        item.progressMsg += `<span style="color:${color}">【${course.courseName}：${course.progress}分】·</span>`
                    })
                }
            }


            //更新本地数据 taskList
            this.taskList = taskList;
            //更新本地页码数据
            this.total = taskListResult.pagination.total;

            // 2. 在DOM更新后恢复之前的选中状态
            this.$nextTick(() => {
                if (this.$refs.examTable) {
                    this.taskList.forEach(row => {
                        if (selectedIds.has(row.id)) {
                            this.$refs.examTable.toggleRowSelection(row, true);
                        }
                    });
                }
            });
        },
        // 上传头像之前的操作
        beforeAvatarUpload(file) {
            //判断格式
            if (file.type != 'image/jpeg') {
                this.$message.error('上传头像图片只能是 JPG 格式!');
                return false
            }

            //判断文件大小
            if (file.size / 1024 > 50) {
                this.$message.error('上传头像图片大小不能超过 50kb!');
                return false
            }

            // 返回true让http-request可以执行
            return true
        },

        // 将文件转换为base64
        fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        },

        // 调整图片尺寸
        resizeImage(file, targetWidth, targetHeight) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        // 创建canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // 设置canvas尺寸
                        canvas.width = targetWidth;
                        canvas.height = targetHeight;

                        // 绘制调整后的图片
                        ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

                        // 将canvas转换为blob
                        canvas.toBlob((blob) => {
                            if (blob) {
                                // 创建新的文件对象，保留原文件名
                                const resizedFile = new File([blob], file.name, {
                                    type: 'image/jpeg',
                                    lastModified: Date.now()
                                });
                                resolve(resizedFile);
                            } else {
                                reject(new Error('Canvas to Blob conversion failed'));
                            }
                        }, 'image/jpeg', 0.95); // 0.95 是压缩质量
                    };
                    img.onerror = () => reject(new Error('Image load failed'));
                    img.src = e.target.result;
                };
                reader.onerror = () => reject(new Error('File read failed'));
                reader.readAsDataURL(file);
            });
        },

        // 上传图片到图库
        async uploadToGallery(file, rowData) {
            try {
                // 先调整图片尺寸
                let processedFile = file;
                if (this.meta.photoWidth && this.meta.photoHeight) {
                    this.$message.info('正在调整图片尺寸...');
                    processedFile = await this.resizeImage(file, this.meta.photoWidth, this.meta.photoHeight);
                }

                // 转换为base64
                const photoBase64 = await this.fileToBase64(processedFile);

                // 调用后端接口上传图片
                const result = await examApi.uploadPhoto({
                    platform: rowData.platform,
                    schoolname: rowData.schoolname,
                    username: rowData.username,
                    photoBase64: photoBase64
                });

                // 由于axios拦截器的处理，成功时result直接就是data内容
                if (result) {
                    // 更新数据
                    rowData.photoBase64 = result.photoBase64;
                    rowData.photoUrl = result.photoUrl;
                    rowData.imageUrl = result.photoUrl; // 用于显示

                    this.$message.success('图片上传成功');
                }
            } catch (error) {
                this.$message.error('图片上传失败，请重试');
            }
        },

        handleAvatarSuccess(res, file, data) {
            // 由于我们在beforeAvatarUpload中返回了false，这个方法不会被调用
            // 所以我们需要手动调用上传方法
            // 该方法实际上不会被触发了
        },

        // 更新网页元数据
        async upMeta() {
            let path = location.pathname.slice(6)
            switch (path) {
                case 'nmwcxt':
                    this.meta.platform = 'nmwcxt';
                    this.meta.name = '柠檬文才考试';
                    this.meta.hasPhotoColumn = true;
                    this.meta.photoWidth = 720;
                    this.meta.photoHeight = 1560;
                    this.meta.photoExt = 'image/jpeg';
                    this.meta.hasCourseTerm = true
                    this.meta.hasCourseName = true
                    this.meta.passProgress = 95
                    break;
                case 'ahjxjy':
                    this.meta.platform = 'ahjxjy';
                    this.meta.name = '安徽继续教育考试';
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                case 'ahjxjyv2':
                    this.meta.platform = 'ahjxjyv2';
                    this.meta.name = '安徽继续教育考试（新）';
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                case 'cxxxt':
                    this.meta.platform = 'cxxxt';
                    this.meta.name = '超星学习通考试'
                    this.meta.hasPhotoColumn = true;
                    this.meta.photoWidth = 229;
                    this.meta.photoHeight = 172;
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                case 'hcjy':
                    this.meta.platform = 'hcjy';
                    this.meta.name = '弘成教育考试'
                    this.meta.hasPhotoColumn = true;
                    this.meta.photoWidth = 320;
                    this.meta.photoHeight = 240;
                    this.meta.photoExt = 'image/jpeg';
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                case 'gjkfdx':
                    this.meta.platform = 'gjkfdx';
                    this.meta.name = '国开考试'
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                case 'gkrj':
                    this.meta.platform = 'gkrj';
                    this.meta.name = '国开软件考试'
                    this.meta.hasPhotoColumn = false;
                    this.meta.hasCourseTerm = false
                    this.meta.hasCourseName = false
                    this.meta.passProgress = 95
                    break;
                default:
                    break;
            }
            await this.updateTaskList();
        },


        // ========= 显示日志弹窗 =========
        //显示日志
        async openLogger(item) {
            let examLog = await examApi.getExamLog({ id: item.id });

            let logMessage = ''
            examLog.forEach(log => {
                logMessage += log
            })
            this.loginfo = logMessage

            this.showLoginfoDialogVisible = true;
        },

        // ========= 显示错误日志弹窗 =========
        //显示错误日志
        openErrorLogger(data) {
            this.loginfo = data;
            this.showErrorDialogVisible = true;
        },

        // ========= 添加任务弹窗 =========
        //格式化数据 '2022022，121312,合肥工业大学。'=>{username:'2022022',password:'121312',schoolname:'合肥工业大学'}
        normalizedData() {
            // 定义字段名
            const fieldNames = ['username', 'password', 'schoolname', 'year', 'term', 'coursename', 'others'];

            // 处理数据字符串，转换为对象数组
            function parseData(dataStr) {
                // 去除首尾的空白字符
                const trimmedData = dataStr.trim();

                // 按换行符拆分成数组
                const lines = trimmedData.split('\n');

                // 处理每一行数据
                const result = lines.map(line => {
                    // 按制表符拆分字段
                    const fields = line.split('\t');

                    // 将字段映射到对象
                    const obj = {};
                    fieldNames.forEach((fieldName, index) => {
                        let value = fields[index] ? fields[index].trim() : ''
                        if (value === '\\') value = ''
                        obj[fieldName] = value
                    });

                    return obj;
                });

                return result;
            }

            // 生成 HTML 表格字符串
            function generateHTMLTable(dataArray, headers) {
                // 开始构建表格
                let html = '<table border="1" cellspacing="0" cellpadding="5">\n';

                // 构建表头
                html += '  <thead>\n    <tr>\n';
                headers.forEach(header => {
                    html += `      <th>${escapeHTML(header)}</th>\n`;
                });
                html += '    </tr>\n  </thead>\n';

                // 构建表体
                html += '  <tbody>\n';
                dataArray.forEach(row => {
                    html += '    <tr>\n';
                    headers.forEach(field => {
                        html += `      <td>${escapeHTML(row[field])}</td>\n`;
                    });
                    html += '    </tr>\n';
                });
                html += '  </tbody>\n';

                // 结束表格
                html += '</table>';

                return html;
            }

            // 辅助函数：转义 HTML 特殊字符，防止 XSS
            function escapeHTML(str) {
                if (typeof str !== 'string') {
                    return str;
                }
                return str.replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }

            // 使用函数处理数据
            let dataArray = parseData(this.courseData);
            // 生成 HTML 表格字符串
            let htmlTableString = generateHTMLTable(dataArray, fieldNames);
            this.courseData = JSON.stringify(dataArray);
            this.courseHtml = htmlTableString;
            this.buttonDisabled = false;
        },

        // ========= 页码栏 =========
        //改变每页显示数量
        async sizeChange(size) {
            this.loading = true
            this.loadingText = '正在加载数据...';
            this.loadingDuration = 2000; // 预计2秒
            this.pageSize = size;
            await this.updateTaskList();
            this.loading = false
            this.$message.warning(`当前为第 ${this.currentPage}页，每页显示 ${this.pageSize} 条数据`);
        },
        //改变页码
        async currentChange(current) {
            this.loading = true
            this.loadingText = '正在加载数据...';
            this.loadingDuration = 2000; // 预计2秒
            this.currentPage = current;
            await this.updateTaskList();
            this.loading = false
            this.$message.warning(`当前为第 ${this.currentPage}页，每页显示 ${this.pageSize} 条数据`);
        },

        // ========= 用户活动检测 =========
        // 记录用户活动
        recordUserActivity() {
            // 如果已经暂停更新，不需要记录活动时间
            if (this.isUpdatePaused) return;

            this.lastActivityTime = Date.now();
        },

        // 处理页面可见性变化
        handleVisibilityChange() {
            if (document.hidden) {
                // 页面不可见时，仅暂停定时器，不显示弹窗
                if (this.refreshTimer && !this.isUpdatePaused) {
                    clearInterval(this.refreshTimer);
                    this.refreshTimer = null;
                }
            } else {
                // 页面可见时，如果更新未被用户主动暂停，则恢复更新
                if (!this.isUpdatePaused && !this.refreshTimer && this.autoRefresh) {
                    this.recordUserActivity(); // 重置活动时间
                    this.updateTaskList(); // 立即刷新数据
                    this.refreshTimer = setInterval(() => {
                        this.updateTaskList();
                    }, 2000);
                }
            }
        },

        // 检查用户是否长时间未操作
        checkUserActivity() {
            // 如果自动刷新未开启，不需要检查
            if (!this.autoRefresh) return;

            const currentTime = Date.now();
            const timeSinceLastActivity = currentTime - this.lastActivityTime;

            // 如果超过限定时间且更新未暂停
            if (timeSinceLastActivity > this.IDLE_TIME_LIMIT && !this.isUpdatePaused) {
                this.pauseUpdate();
            }
        },

        // 暂停更新
        pauseUpdate() {
            // 清除刷新定时器
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
            }

            // 暂停活动检测
            if (this.activityCheckTimer) {
                clearInterval(this.activityCheckTimer);
                this.activityCheckTimer = null;
            }

            this.isUpdatePaused = true;

            // 弹出提示框
            this.$confirm('检测到您长时间未操作，已暂停数据更新。', '提示', {
                confirmButtonText: '开始更新',
                cancelButtonText: '稍后再说',
                type: 'info',
                closeOnClickModal: false,
                closeOnPressEscape: false,
                showClose: false
            }).then(() => {
                this.resumeUpdate();
            }).catch(() => {
                // 用户点击取消，保持暂停状态
                // 添加一次性点击监听器，用户点击页面任意位置即可恢复
                const resumeOnClick = () => {
                    document.removeEventListener('click', resumeOnClick);
                    this.$message.success('数据更新已恢复');
                    this.resumeUpdate();
                };

                // 延迟添加监听器，避免立即触发
                setTimeout(() => {
                    document.addEventListener('click', resumeOnClick, { once: true });
                }, 100);

                this.$message({
                    message: '数据更新已暂停，点击页面任意位置可恢复更新',
                    type: 'info',
                    duration: 5000
                });
            });
        },

        // 恢复更新
        resumeUpdate() {
            this.isUpdatePaused = false;
            this.recordUserActivity(); // 重置活动时间

            // 如果自动刷新开关开启，重新启动定时刷新
            if (this.autoRefresh) {
                // 立即刷新一次数据
                this.updateTaskList();

                // 重新启动定时刷新
                this.refreshTimer = setInterval(() => {
                    this.updateTaskList();
                }, 2000);
            }

            // 恢复活动检查
            if (!this.activityCheckTimer) {
                this.activityCheckTimer = setInterval(() => {
                    this.checkUserActivity();
                }, 60000); // 每分钟检查一次
            }
        },

        // 设置用户活动监听器
        setupActivityListeners() {
            // 监听的事件类型
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

            // 添加事件监听器
            events.forEach(event => {
                document.addEventListener(event, this.recordUserActivity, true);
            });

            // 设置活动检查定时器，每分钟检查一次
            this.activityCheckTimer = setInterval(() => {
                this.checkUserActivity();
            }, 60000); // 每分钟检查一次

            // 监听页面可见性变化
            document.addEventListener('visibilitychange', this.handleVisibilityChange);
        },

        // 清理活动监听器
        removeActivityListeners() {
            const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

            events.forEach(event => {
                document.removeEventListener(event, this.recordUserActivity, true);
            });

            if (this.activityCheckTimer) {
                clearInterval(this.activityCheckTimer);
                this.activityCheckTimer = null;
            }

            // 移除页面可见性监听
            document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        }


    },
    async created() {
        await this.upMeta();
        this.updateTaskList();

        // 设置用户活动监听器
        this.setupActivityListeners();

        if (location.href.includes('coursebak')) {
            this.isAdmin = true;
        }
    },
    mounted() {
        this.handleRefreshChange(true)
    },
    beforeDestroy() {
        // 清理定时器
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }

        // 清理活动监听器
        this.removeActivityListeners();
    },
    components: {
        'downloadExcel': JsonExcel,
        'Loading': Loading
    },
    watch: {
        $route: 'upMeta'
    },
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    overflow: hidden;
}

// 数据更新状态指示器
.update-status-bar {
    position: fixed;
    top: 60px; // 考虑到可能有导航栏，调整位置
    left: 50%;
    transform: translateX(-50%);
    background-color: #fff3cd;
    color: #856404;
    padding: 8px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    z-index: 1000;
    border-radius: 4px;
    animation: slideDown 0.3s ease-out;

    i {
        font-size: 18px;
        color: #faad14;
    }

    span {
        font-size: 14px;
    }

    .el-button {
        margin-left: 10px;
        font-weight: bold;
        &:hover {
            color: #d48806;
        }
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.control {
    margin: 20px 0;
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;

    .search-section {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e4e7ed;

        .el-input {
            width: 200px;
        }

        .el-select {
            width: 150px;
        }
    }

    .action-section {
        display: flex;
        align-items: center;
        gap: 20px;
        flex-wrap: wrap;

        .button-group {
            display: flex;
            align-items: center;
            gap: 10px;
            padding-right: 20px;
            border-right: 1px solid #e4e7ed;

            &:last-child {
                border-right: none;
                padding-right: 0;
            }

            .switch-container {
                display: flex;
                align-items: center;
                height: 32px; // 与按钮高度保持一致
            }
        }
    }
}

.content_button {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    & > * {
        margin-top: 5px;
    }
}

.image {
    height: 100px;
}

.avatar-uploader {
    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        width: 80px;
        height: 80px;

        &:hover {
            border-color: #409eff;
        }
    }

    .avatar {
        width: 80px;
        height: 80px;
        display: block;
        object-fit: cover;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 80px;
        height: 80px;
        line-height: 80px;
        text-align: center;
    }
}

.logger {
    text-align: left;
    line-height: 15px;
    height: 100px;
    overflow: auto;
}
</style>