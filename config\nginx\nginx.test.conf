user root; # 以 root 权限运行 Nginx
worker_processes auto; # 自动检测和分配工作进程数


events {
    worker_connections 1024; # 每个工作进程的最大连接数
}


http {
    # HTTP全局设置
    include mime.types; # 包含 MIME 类型文件，用于正确识别资源类型
    default_type application/octet-stream; # 设置默认 MIME 类型
    server_tokens off; # 控制是否在错误页面和响应头中显示 Nginx 的版本号

    # 日志设置
    log_format main '$remote_addr - $remote_user [$time_local] "$request" ' '$status $body_bytes_sent "$http_referer" ' '"$http_user_agent" "$http_x_forwarded_for"'; # 定义日志格式
    access_log /var/log/nginx/access.log main; # 指定访问日志文件路径及使用的日志格式
    error_log /var/log/nginx/error.log notice; # 指定错误日志文件路径及日志级别

    # HTTP连接
    keepalive_requests 100000; # 设置保持连接的最大请求数
    limit_conn_zone $binary_remote_addr zone=perip:10m; # 基于客户端 IP 的连接限制
    limit_conn_zone $server_name zone=perserver:10m; # 基于服务器名称的连接限制

    # HTTP请求
    client_header_buffer_size 32k; # 缓冲客户端请求头的内存大小
    client_max_body_size 50m; # 限制文件上传大小
    client_body_buffer_size 512k;

    # HTTP响应
    expires 3d; # 禁用静态资源缓存
    gzip on; # 启用 Gzip 压缩
    gzip_min_length 1k; # 设置启用压缩的最小响应体长度
    gzip_buffers 4 16k; # 设置 Gzip 压缩缓冲区
    gzip_comp_level 2; # 设置 Gzip 压缩级别
    gzip_types text/plain application/javascript text/css application/xml application/json text/xml ;# 指定启用 Gzip 压缩的 MIME 类型
    gzip_vary on; # 添加 Vary: Accept-Encoding 响应头
    gzip_proxied expired no-cache no-store private auth; # 指定何时压缩代理响应
    gzip_disable "MSIE [1-6]\."; # 当客户端的 User-Agent 识别为旧版本的 Internet Explorer（IE）时，禁用 Gzip 压缩。

    # 反向代理：内存缓冲
    proxy_buffer_size 32k; # 反代响应头的缓存
    proxy_buffers 4 64k; # 反代响应体的缓存

    # 反向代理 错误处理
    proxy_next_upstream error timeout invalid_header http_500 http_503 http_404; # 设置下一步上游服务器处理的错误条件


    # 开发环境，直接用vscode启动
    server {
        # web服务器设置
        listen 80 ; # 监听端口
        server_name test.localhost; # 服务器名称，可以是域名或 IP 地址。


        # 日志配置
        access_log /www/sites/courseWeb/log/access.log main; # 访问日志文件路径及日志格式
        error_log /www/sites/courseWeb/log/error.log; # 错误日志文件路径 error日志不接受日志级别

        # 路由配置：反向代理配置 test
        location ^~/api/ {
            # 容器不能通过127.0.0.1访问主机，可以使用host.docker.internal
            # host.docker.internal 是在 macOS 和 Windows 中 Docker 的默认名称，用来指向宿主机。要在 Linux 中启用它，你需要在 Docker 运行时手动定义这个主机名。
            # 可以直接写如linunx的Host echo "************* host.docker.internal" >> /etc/hosts
            proxy_pass http://courseServer:3000;
            proxy_http_version 1.1; # 设置 HTTP 协议版本为 1.1

            # 保留客户端请求的 Host、IP等信息，传递给后端
            proxy_set_header Host $host; # 原始客户端请求的 Host(域名)
            proxy_set_header X-Real-IP $remote_addr; # 客户端的真实 IP
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 代理链上的所有 IP
            proxy_set_header X-Forwarded-Host $host; # 也可用 $server_name，看后端如何解析

            # 网站设置
            # error_page 502 /backend_down.html; # 当发生 502 错误时返回 backend_down.html 页
        }

        # history模式 try_files指令，用来尝试按顺序找到第一个存在的文件，并返回它。
        # $uri：这是Nginx的一个内置变量，表示请求的URI，
        location / {
            root /www/sites/courseWeb/index; # 网站资源根目录
            try_files $uri $uri/ /index.html;
            index index.php index.html index.htm default.php default.htm default.html; # 默认的索引文件列表
            # error_page 404 /404.html; # 自定义 404 错误页面

        }

        # root 会把url中的path和root配置拼接起来
        # alias 等于url中的path减去loction配置，然后加上alias配置
        location ^~ /avatar/ {
            root /www/sites/courseWeb/upload; # 末尾不能加 /
        }
    }
}