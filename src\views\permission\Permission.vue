<template>
    <div class="article-list" v-loading="loading" element-loading-text="加载中...">
        <!-- 标题栏 -->
        <div class="user-title">&nbsp;&nbsp;权限管理</div>

        <!-- 控制栏 -->
        <div class="control">
            <div class="left">
                <el-button type="primary" icon="el-icon-plus" size="medium" @click="handleAdd" v-if="isAdmin">新增权限</el-button>
                <el-button type="danger" icon="el-icon-delete" size="medium" @click="handleBatchDelete" v-if="isAdmin">批量删除</el-button>
                <el-button type="warning" icon="el-icon-refresh" size="medium" @click="toggleView">
                    {{ viewMode === 'list' ? '树形视图' : '列表视图' }}
                </el-button>
            </div>
            <div class="right">
                <el-input placeholder="请输入权限名称、路径或组件搜索" v-model="searchKeyword" clearable @keyup.enter="handleSearch">
                    <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
                </el-input>
            </div>
        </div>

        <!-- 列表视图 -->
        <el-table v-if="viewMode === 'list' && permissionList.length > 0" :key="tableKey" :data="permissionList" :row-key="permission => permission.permission_id" border @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="ID" prop="permission_id" width="80" align="center" />
            <el-table-column label="权限名称" align="center">
                <template slot-scope="scope">
                    <el-link type="primary" @click="handleEdit(scope.row)" :underline="false">
                        {{ scope.row.name }}
                    </el-link>
                </template>
            </el-table-column>
            <el-table-column label="路径" prop="path" align="center" />
            <el-table-column label="组件" prop="component" align="center" show-overflow-tooltip />
            <el-table-column label="图标" align="center" width="100">
                <template slot-scope="scope">
                    <i :class="scope.row.icon" v-if="scope.row.icon"></i>
                </template>
            </el-table-column>
            <el-table-column label="父级ID" prop="pid" width="80" align="center" />
            <el-table-column label="排序" prop="orderNum" width="80" align="center" />
            <el-table-column label="是否隐藏" width="100" align="center">
                <template slot-scope="scope">
                    <el-tag :type="scope.row.isHidden === 1 ? 'danger' : 'success'">
                        {{ scope.row.isHidden === 1 ? '隐藏' : '显示' }}
                    </el-tag>
                </template>
            </el-table-column>
        </el-table>

        <!-- 树形视图 -->
        <el-tree v-if="viewMode === 'tree'" :data="permissionTree" :props="treeProps" node-key="permission_id" default-expand-all :expand-on-click-node="false" style="margin-top: 20px;">
            <span class="custom-tree-node" slot-scope="{ data }">
                <span>
                    <i :class="data.icon" style="margin-right: 8px;"></i>
                    <el-link type="primary" @click="handleEdit(data)" :underline="false">
                        {{ data.name }}
                    </el-link>
                    <span style="color: #909399; margin-left: 10px;">{{ data.path }}</span>
                </span>
                <span>
                    <el-tag size="mini" :type="data.isHidden === 1 ? 'danger' : 'success'">
                        {{ data.isHidden === 1 ? '隐藏' : '显示' }}
                    </el-tag>
                </span>
            </span>
        </el-tree>

        <!-- 分页（仅列表视图） -->
        <el-pagination v-if="viewMode === 'list'" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.currentPage" :page-sizes="[10, 20, 30, 50]" :page-size="pagination.pageSize" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total" style="margin-top: 20px; text-align: right;">
        </el-pagination>

        <!-- 新增/编辑权限弹窗 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
            <el-form :model="permissionForm" :rules="permissionRules" ref="permissionForm" label-width="100px">
                <el-form-item label="权限名称" prop="name">
                    <el-input v-model="permissionForm.name" placeholder="请输入权限名称"></el-input>
                </el-form-item>
                <el-form-item label="路径" prop="path">
                    <el-input v-model="permissionForm.path" placeholder="请输入路径，如：/user"></el-input>
                </el-form-item>
                <el-form-item label="父级权限" prop="pid">
                    <el-select v-model="permissionForm.pid" placeholder="请选择父级权限">
                        <el-option label="顶级权限" :value="-1"></el-option>
                        <el-option v-for="item in parentPermissions" :key="item.permission_id" :label="item.name" :value="item.permission_id" :disabled="isEdit && item.permission_id === permissionForm.permission_id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="组件路径" prop="component">
                    <el-input v-model="permissionForm.component" placeholder="请输入组件路径，如：/user/User.vue"></el-input>
                </el-form-item>
                <el-form-item label="图标" prop="icon">
                    <el-input v-model="permissionForm.icon" placeholder="请输入图标类名，如：fas fa-user">
                        <template slot="append">
                            <i :class="permissionForm.icon" v-if="permissionForm.icon"></i>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item label="排序" prop="orderNum">
                    <el-input-number v-model="permissionForm.orderNum" :min="1" :max="999"></el-input-number>
                </el-form-item>
                <el-form-item label="是否隐藏" prop="isHidden">
                    <el-switch v-model="permissionForm.isHidden" :active-value="1" :inactive-value="0" active-text="隐藏" inactive-text="显示">
                    </el-switch>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button type="danger" @click="handleDeleteInDialog" v-if="isEdit && isAdmin">删 除</el-button>
            </div>
        </el-dialog>

    </div>
</template>

<script>
import * as permissionApi from '../../api/permissionApi.js';

export default {
    data() {
        return {
            loading: false, // 加载状态

            // 权限控制
            isAdmin: this.$store.state.user.userInfo.user_role_id == '1',

            // 视图模式
            viewMode: 'list', // 'list' 或 'tree'

            // 表格相关
            permissionList: [], // 权限列表数据
            permissionTree: [], // 权限树形数据
            tableKey: 0, // 用于强制重新渲染表格
            multipleSelection: [], // 多选数据

            // 搜索相关
            searchKeyword: '', // 搜索关键词

            // 分页相关
            pagination: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },

            // 弹窗相关
            dialogVisible: false, // 弹窗显示控制
            dialogTitle: '新增权限', // 弹窗标题
            isEdit: false, // 是否编辑模式

            // 表单数据
            permissionForm: {
                permission_id: null,
                name: '',
                path: '',
                pid: -1,
                orderNum: 1,
                icon: '',
                component: '',
                isHidden: 0
            },

            // 表单验证规则
            permissionRules: {
                name: [
                    { required: true, message: '请输入权限名称', trigger: 'blur' },
                    { min: 2, max: 50, message: '权限名称长度在 2 到 50 个字符', trigger: 'blur' }
                ],
                path: [
                    { required: true, message: '请输入路径', trigger: 'blur' }
                ],
                pid: [
                    { required: true, message: '请选择父级权限', trigger: 'change' }
                ],
                orderNum: [
                    { required: true, message: '请输入排序号', trigger: 'blur' }
                ]
            },

            // 父级权限列表
            parentPermissions: [],

            // 树形控件配置
            treeProps: {
                children: 'children',
                label: 'name'
            }
        };
    },

    methods: {
        // ========= 工具函数 =========
        // 切换视图模式
        toggleView() {
            this.viewMode = this.viewMode === 'list' ? 'tree' : 'list';
            if (this.viewMode === 'tree') {
                this.getPermissionTree();
            } else {
                this.getPermissionList();
            }
        },

        // ========= 数据获取 =========
        // 获取权限列表
        async getPermissionList() {
            this.loading = true;
            try {
                const params = {
                    page: this.pagination.currentPage,
                    pageSize: this.pagination.pageSize,
                    search: this.searchKeyword
                };

                const res = await permissionApi.getPermissionList(params);
                console.log('权限列表响应:', res);

                // axios拦截器已经返回了data部分，所以res就是data对象
                if (res) {
                    // 使用深拷贝处理数据，避免引用问题
                    const listData = JSON.parse(JSON.stringify(res.list || []));
                    // 增加表格key，强制重新渲染
                    this.tableKey += 1;
                    // 使用this.$nextTick确保在DOM更新循环结束后再赋值
                    this.$nextTick(() => {
                        this.permissionList = listData;
                    });

                    this.pagination.total = res.total || 0;
                    this.pagination.currentPage = res.page || 1;
                    this.pagination.pageSize = res.pageSize || 10;
                }
            } catch (error) {
                console.error('获取权限列表失败:', error);
                this.$message.error('获取权限列表失败，请稍后重试');
            } finally {
                this.loading = false;
            }
        },

        // 获取权限树形结构
        async getPermissionTree() {
            this.loading = true;
            try {
                const res = await permissionApi.getPermissionTree();
                console.log('权限树响应:', res);

                if (res) {
                    this.permissionTree = res;
                }
            } catch (error) {
                console.error('获取权限树失败:', error);
                this.$message.error('获取权限树失败');
            } finally {
                this.loading = false;
            }
        },

        // 获取所有权限用于父级选择
        async getAllPermissions() {
            try {
                const res = await permissionApi.getPermissionList({ pageSize: 1000 });
                if (res && res.list) {
                    this.parentPermissions = res.list;
                }
            } catch (error) {
                console.error('获取父级权限列表失败:', error);
            }
        },

        // ========= 表格操作 =========
        // 处理多选
        handleSelectionChange(val) {
            this.multipleSelection = val;
        },

        // 搜索
        handleSearch() {
            this.pagination.currentPage = 1;
            this.getPermissionList();
        },

        // ========= 分页操作 =========
        handleSizeChange(val) {
            this.pagination.pageSize = val;
            this.getPermissionList();
        },

        handleCurrentChange(val) {
            this.pagination.currentPage = val;
            this.getPermissionList();
        },

        // ========= CRUD操作 =========
        // 新增权限
        handleAdd() {
            this.dialogTitle = '新增权限';
            this.isEdit = false;
            this.dialogVisible = true;
            this.resetForm();
        },

        // 编辑权限
        handleEdit(row) {
            this.dialogTitle = '编辑权限';
            this.isEdit = true;
            this.permissionForm = {
                permission_id: row.permission_id,
                name: row.name,
                path: row.path,
                pid: row.pid,
                orderNum: row.orderNum,
                icon: row.icon || '',
                component: row.component || '',
                isHidden: row.isHidden
            };
            this.dialogVisible = true;
        },

        // 批量删除
        async handleBatchDelete() {
            if (this.multipleSelection.length === 0) {
                this.$message.error('请先选择要删除的权限');
                return;
            }

            this.$confirm(`确定要删除选中的 ${this.multipleSelection.length} 个权限吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 收集所有要删除的权限ID
                    const permissionIds = this.multipleSelection.map(permission => permission.permission_id);

                    // 根据API文档，批量删除时传递所有权限ID
                    await permissionApi.deletePermission({
                        permission_ids: permissionIds,
                        permission_id: permissionIds[0] // 使用第一个ID
                    });

                    this.$message.success(`成功删除权限`);
                    await this.getPermissionList();
                } catch (error) {
                    console.error('批量删除失败:', error);
                    this.$message.error(error || '删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 在编辑弹窗中删除权限
        async handleDeleteInDialog() {
            const row = {
                permission_id: this.permissionForm.permission_id,
                name: this.permissionForm.name
            };

            this.$confirm(`确定要删除权限 "${row.name}" 吗？`, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.loading = true;
                try {
                    // 根据API文档，需要同时传递permission_ids数组和permission_id
                    await permissionApi.deletePermission({
                        permission_ids: [row.permission_id],
                        permission_id: row.permission_id
                    });
                    this.$message.success('删除成功');
                    this.dialogVisible = false; // 关闭弹窗

                    // 根据当前视图模式刷新数据
                    if (this.viewMode === 'list') {
                        await this.getPermissionList();
                    } else {
                        await this.getPermissionTree();
                    }
                } catch (error) {
                    console.error('删除权限失败:', error);
                    this.$message.error(error || '删除失败，请稍后重试');
                } finally {
                    this.loading = false;
                }
            }).catch(() => {
                this.$message.info('已取消删除');
            });
        },

        // 提交表单
        submitForm() {
            this.$refs.permissionForm.validate(async (valid) => {
                if (valid) {
                    this.loading = true;
                    try {
                        // 准备提交数据，确保数据类型正确
                        const submitData = {
                            ...this.permissionForm,
                            pid: Number(this.permissionForm.pid),
                            orderNum: Number(this.permissionForm.orderNum),
                            isHidden: this.permissionForm.isHidden ? 1 : 0
                        };

                        if (this.isEdit) {
                            // 编辑模式
                            await permissionApi.updatePermission(submitData);
                            this.$message.success('修改成功');
                        } else {
                            // 新增模式 - 删除permission_id
                            delete submitData.permission_id;
                            // API文档中创建权限的pid字段有多余空格，需要处理
                            const createData = {
                                name: submitData.name,
                                path: submitData.path,
                                'pid ': submitData.pid, // 注意这里有空格
                                orderNum: submitData.orderNum,
                                icon: submitData.icon,
                                component: submitData.component,
                                isHidden: Boolean(submitData.isHidden)
                            };
                            await permissionApi.createPermission(createData);
                            this.$message.success('新增成功');
                        }

                        this.dialogVisible = false;

                        // 刷新数据和父级权限列表
                        await this.getAllPermissions();
                        if (this.viewMode === 'list') {
                            await this.getPermissionList();
                        } else {
                            await this.getPermissionTree();
                        }
                    } catch (error) {
                        console.error('操作失败:', error);
                        this.$message.error(error || '操作失败，请稍后重试');
                    } finally {
                        this.loading = false;
                    }
                } else {
                    console.log('表单验证失败');
                    return false;
                }
            });
        },

        // 重置表单
        resetForm() {
            this.permissionForm = {
                permission_id: null,
                name: '',
                path: '',
                pid: -1,
                orderNum: 1,
                icon: '',
                component: '',
                isHidden: 0
            };

            this.$nextTick(() => {
                this.$refs.permissionForm && this.$refs.permissionForm.clearValidate();
            });
        }
    },

    async created() {
        // 页面加载时获取数据
        await this.getAllPermissions();
        await this.getPermissionList();
    }
};
</script>

<style lang="scss" scoped>
.article-list {
    background-color: white;
    padding: 20px;
    min-height: calc(100vh - 100px);
}

.user-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
}

.control {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .left {
        display: flex;
        gap: 10px;
    }

    .right {
        .el-input {
            width: 400px;
        }
    }
}

.dialog-footer {
    text-align: right;
}

// 树形视图样式
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
}

// 加载动画样式
::v-deep .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.9);
}
</style>
