#!/bin/sh
# 指定脚本使用的 Shell 解释器为 /bin/sh

# 根据环境变量 ENV 的值，选择相应的 Nginx 配置文件
echo "容器运行前的环境变量为：$ENV"
if [ "$ENV" = "DEV" ]; then
    # 如果是开发环境，复制开发配置文件为默认的 nginx.conf
    cp /etc/nginx/nginx.dev.conf /etc/nginx/nginx.conf
elif [ "$ENV" = "TEST" ]; then
    # 如果是测试环境，复制测试配置文件为默认的 nginx.conf
    cp /etc/nginx/nginx.test.conf /etc/nginx/nginx.conf
elif [ "$ENV" = "PROD" ]; then
    # 如果是生产环境，复制生产配置文件为默认的 nginx.conf
    cp /etc/nginx/nginx.prod.conf /etc/nginx/nginx.conf
else
    # 如果 ENV 变量未设置或不匹配，提示错误并退出
    echo "错误：未识别的环境变量 ENV，必须为 'dev'、'test' 或 'prod'"
    exit 1
fi

# 执行传递给脚本的命令参数
exec "$@"