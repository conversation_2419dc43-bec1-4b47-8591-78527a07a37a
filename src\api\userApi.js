import axios from "@/utils/axios.js";
import apiPath from "../../config/apiPath";
let { userApiPath } = apiPath;

//3.用户登录
export let signin = (userInfo) => axios.post(userApiPath.userSignin, userInfo);

//4.根据token获取用户菜单
export let getMenus = (userInfo) => axios.get(userApiPath.getUserMenus);

//5.token登录
export let validate = (token) => axios.get(userApiPath.userValidate);

//6.获取用户列表
export let getUserList = (params) => axios.get(userApiPath.getUserList, { params });

//7.创建用户
export let createUser = (userData) => axios.post(userApiPath.createUser, userData);

//8.删除用户
export let deleteUser = (data) => axios.post(userApiPath.deleteUser, data);

//9.修改用户
export let updateUser = (userData) => axios.post(userApiPath.updateUser, userData);

//10.获取角色列表
export let getRoleList = () => axios.get('/api/admin/role/getList');
