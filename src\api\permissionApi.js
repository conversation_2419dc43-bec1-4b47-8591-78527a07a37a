import axios from "@/utils/axios.js";
import apiPath from '../../config/apiPath';

let { permissionApiPath } = apiPath;

// 权限管理API

// 1.获取权限树形结构
export let getPermissionTree = () => axios.get(permissionApiPath.getPermissionTree);

// 2.获取权限列表（平铺结构）
export let getPermissionList = (params) => axios.get(permissionApiPath.getPermissionList, { params });

// 3.创建权限
export let createPermission = (data) => axios.post(permissionApiPath.createPermission, data);

// 4.修改权限
export let updatePermission = (data) => axios.post(permissionApiPath.updatePermission, data);

// 5.删除权限
export let deletePermission = (data) => axios.post(permissionApiPath.deletePermission, data); 