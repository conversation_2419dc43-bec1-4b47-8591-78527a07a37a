import axios from "@/utils/axios.js";
import apiPath from "../../config/apiPath";

let {courseApiPath}=apiPath


//1.获取任务列表
export let getCourseList=(data)=>axios.post(courseApiPath.getCourseList,data)

//2.添加一个上课任务
export let addCourseTask=(data)=>axios.post(courseApiPath.addCourseTask,data)

//3.删除一个上课任务
export let delCourseTask=(data)=>axios.post(courseApiPath.delCourseTask,data)

//4.修改一个任务信息
export let updateCourseTask=(data)=>{
    delete data['logMessage']
    return axios.post(courseApiPath.updateCourseTask,data)
}

//5.开始一个上课任务
export let runCourseTask=(data)=>axios.post(courseApiPath.runCourseTask,data)

//6.提交图片的base64编码

//7.获取用户头像的base64编码

//8.清空上课任务的日志
export let clearCourseLog=(data)=>{
    delete data['logMessage']
    return axios.post(courseApiPath.clearCourseLog,data)
}

//9.获取具体的任务日志
export let getCourseLog=(data)=>axios.post(courseApiPath.getCourseLog,data)

//10.获取网课统计数据
export let getCourseStat=()=>axios.get(courseApiPath.getCourseStat)