import axios from "@/utils/axios.js";

// 角色管理API

// 1.获取角色列表
export let getRoleList = (params) => axios.get('/api/admin/role/getList', { params });

// 2.创建角色
export let createRole = (data) => axios.post('/api/admin/role/create', data);

// 3.删除角色
export let deleteRole = (data) => axios.post('/api/admin/role/delete', data);

// 4.获取角色的权限列表
export let getRolePermissions = (params) => axios.get('/api/admin/rolePermission/getRolePermissions', { params });

// 5.为角色分配权限
export let assignPermissions = (data) => axios.post('/api/admin/rolePermission/assignPermissions', data); 