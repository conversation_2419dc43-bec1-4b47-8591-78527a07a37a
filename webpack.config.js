let path = require('path');

//webpackplugins
let { VueLoaderPlugin } = require('vue-loader');
let HtmlWebpackPlugin = require('html-webpack-plugin');
let MiniCssExtractPlugin = require('mini-css-extract-plugin');
let { CleanWebpackPlugin } = require('clean-webpack-plugin');
// const BundleAnalyzerPlugin =
// 	require('webpack-bundle-analyzer').BundleAnalyzerPlugin;

module.exports = {
	// ========= 预设 =========
	mode: 'development',

	// ========= 入口文件 =========
	entry: {
		main: './src/main.js',
	},

	// ========= 打包后的目录 =========
	output: {
		path: path.resolve('dist/index'),
	},

	// ========= 路径配置 =========
	resolve: {
		//路径别名
		alias: {
			'@': path.resolve('src'),
			'@c': path.resolve('src/components'),
			'@v': path.resolve('src/views'),
			vue$: 'vue/dist/vue.esm.js',
		},
		extensions: ['.js', '.vue'], //在引用模块的时候如果没有路径自动添加路径
	},

	// ========= sourcemap =========
	devtool: 'source-map',

	// ========= 代码监听 =========
	watch: false,

	// ========= loader =========
	module: {
		rules: [
			//.vue
			{
				test: /\.vue$/,
				use: 'vue-loader',
			},
			//.js
			{
				test: /\.js$/,
				use: [
					{
						loader: 'babel-loader',
						options: {
							presets: [
								[
									'@babel/preset-env',
									{
										useBuiltIns: 'usage',
										corejs: '3',
										targets: 'defaults',
									},
								],
								'@vue/babel-preset-jsx',
							],
						},
					},
				],
			},
			//.css
			{
				test: /\.(css|scss)/,
				use: [
					MiniCssExtractPlugin.loader,
					{
						loader: 'css-loader',
						options: {
							// esModule: false,
						},
					},
					{
						loader: 'sass-loader',
						options: {
							implementation: require('sass'),
						},
					},
				],
			},
			//图片
			{
				test: /\.(jpg|png|gif|bmp)$/,
				use: [
					{
						loader: 'url-loader',
						options: {
							name: '[hash:10].[ext]',
							// esModule: false,
							limit: 32 * 1024,
							outputPath: 'images', //指定输出图片的目录images目录
							publicPath: '/images', //返回的路径添加/images
						},
					},
				],
			},
			//视频，字体等
			{
				test: /.(avi|mp3|ttf|woff2?|eot|svg)$/,
				type: 'asset/resource',
				generator: {
					filename: 'resources/[hash:10][ext]',
				},
			},
		],
	},

	// ========= 插件 =========
	plugins: [
		//mini-css-extract-plugin：把css单独放到一个文件中，而不是放到style标签中
		new MiniCssExtractPlugin({
			filename: 'style.css', //打包后的css文件名  '[name].css'
			// chunkFilename: '[id].css' //也就是说一个chunk对应一个css文件,如果不配置，会使用 filename 作为非入口（异步）chunk 的文件名。
			// ignoreOrder: false // 忽略有关文件顺序的警告。默认为 false。
			// esModule: true // 如何在 JavaScript 中导入生成的 CSS。
		}),
		//VueLoaderPlugin：负责vue文件的三部分资源正确处理，scoped属性处理，热模块替换
		new VueLoaderPlugin(), //vue-loader内置插件

		//html-webpack-plugin：把打包后的js文件插入到指定的html文件中
		new HtmlWebpackPlugin({
			template: './public/template.html', //模板文件路径，这里是相对于根路径
			filename: 'index.html', //打包后的文件名
			publicPath: '/', //html中src引用js目录前面加上/ 代表从当前，否则会添加子目录
			// inject: 'body',  //定 script 标签应该插入到 HTML 文件的哪个位置，可选值有 'head', 'body' 或者 false。
			// title: 'My App', //指定 HTML 的 <title> 标签内容
			// 决定是否对生成的 HTML 文件进行压缩，以及如何压缩。
			// minify: {
			// 	removeComments: true,
			// 	collapseWhitespace: true,
			// },
			// chunks: ['app'], //指定哪些 chunks 应该被添加到生成的 HTML 文件中。默认情况下，所有入口 chunks 都会被添加。
			// excludeChunks: ['test'], //排除一些不需要添加到 HTML 文件中的 chunks。
			// hash: true //如果设置为 true，将会在所有包含的脚本和 CSS 文件后添加一个唯一的编译 hash。这对于缓存清除非常有用。
		}),

		//clean-webpack-plugin：清空打包后的目录
		new CleanWebpackPlugin({
			// cleanOnceBeforeBuildPatterns: ['*.js'] //指定在构建之前需要删除的模式数组。
		}),
		//webpack-bundle-analyzer：分析和可视化Webpack生成的包（bundle）的插件。它会创建一个交互式的分析报告，展示各个包和模块的大小、依赖关系等信息。
		// new BundleAnalyzerPlugin(),
	],
};
