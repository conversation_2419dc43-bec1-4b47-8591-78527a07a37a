<template>
  <div class="loading-overlay" v-if="visible">
    <div class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
        <div class="loading-text">{{ text }}</div>
        <div class="progress-wrapper" v-if="duration > 0">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
          </div>
          <div class="progress-text">{{ Math.round(progress) }}%</div>
        </div>
        <div class="time-info" v-if="duration > 0">
          预计剩余时间：{{ formatTime(remainingTime) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    // 是否显示loading
    visible: {
      type: Boolean,
      default: false
    },
    // loading文本
    text: {
      type: String,
      default: '正在进行任务中...'
    },
    // 预计持续时间（毫秒）
    duration: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      progress: 0,
      startTime: null,
      timer: null,
      remainingTime: 0
    };
  },
  watch: {
    visible(val) {
      if (val && this.duration > 0) {
        this.startProgress();
      } else {
        this.stopProgress();
      }
    },
    duration(val) {
      if (this.visible && val > 0) {
        this.startProgress();
      }
    }
  },
  methods: {
    startProgress() {
      this.progress = 0;
      this.startTime = Date.now();
      this.remainingTime = this.duration;
      
      // 清除之前的定时器
      if (this.timer) {
        clearInterval(this.timer);
      }
      
      // 每100ms更新一次进度
      this.timer = setInterval(() => {
        const elapsed = Date.now() - this.startTime;
        const percentage = Math.min((elapsed / this.duration) * 100, 99); // 最多到99%，避免100%但任务未完成
        this.progress = percentage;
        this.remainingTime = Math.max(this.duration - elapsed, 0);
        
        // 如果超时了，停留在99%
        if (elapsed >= this.duration) {
          this.progress = 99;
          clearInterval(this.timer);
        }
      }, 100);
    },
    stopProgress() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.progress = 0;
      this.startTime = null;
      this.remainingTime = 0;
    },
    formatTime(milliseconds) {
      const seconds = Math.ceil(milliseconds / 1000);
      if (seconds < 60) {
        return `${seconds}秒`;
      }
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      if (minutes < 60) {
        return remainingSeconds > 0 ? `${minutes}分${remainingSeconds}秒` : `${minutes}分钟`;
      }
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      return `${hours}小时${remainingMinutes}分钟`;
    }
  },
  beforeDestroy() {
    this.stopProgress();
  }
};
</script>

<style lang="scss" scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-container {
  text-align: center;
}

.loading-content {
  background: white;
  padding: 40px 60px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  min-width: 300px;
}

.loading-spinner {
  margin-bottom: 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
}

.progress-wrapper {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #409eff;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-text {
  font-size: 14px;
  color: #909399;
}

.time-info {
  margin-top: 10px;
  font-size: 14px;
  color: #909399;
}
</style>
