import axios from "@/utils/axios.js";
import apiPath from "../../config/apiPath";

let { clusterApiPath } = apiPath;

// console.log(clusterApiPath)

//1.获取进程列表
export let getClusterList = (data) => axios.post(clusterApiPath.getClusterList, data);

//2.添加一个进程
export let addCluster = (data) => axios.post(clusterApiPath.addCluster, data);

//3.删除一个进程
export let delCluster = (data) => axios.post(clusterApiPath.delCluster, data);

// 4.重置一个进程
export let resetCluster = (data) => axios.post(clusterApiPath.resetCluster, data);

// 5.根据任务id返回pcid和workId
export let getClusterInfoByTaskId = (data) => axios.post(clusterApiPath.getClusterInfoByTaskId, data);

// 6.获取进程统计数据
export let getClusterStat = () => axios.get(clusterApiPath.getClusterStat);

