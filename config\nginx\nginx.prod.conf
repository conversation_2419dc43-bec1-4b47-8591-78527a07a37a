include /etc/nginx/custom/prodMain.conf;



user root; # 以 root 权限运行 Nginx
worker_processes auto; # 自动检测和分配工作进程数


events {
    worker_connections 1024; # 每个工作进程的最大连接数
}


http {
    # HTTP全局设置

    include /etc/nginx/custom/prodHttp.conf;


    include mime.types; # 包含 MIME 类型文件，用于正确识别资源类型
    default_type application/octet-stream; # 设置默认 MIME 类型
    server_tokens off; # 控制是否在错误页面和响应头中显示 Nginx 的版本号

    # 日志设置
    log_format main '$remote_addr - $remote_user [$time_local] "$request" ' '$status $body_bytes_sent "$http_referer" ' '"$http_user_agent" "$http_x_forwarded_for"'; # 定义日志格式
    access_log /var/log/nginx/access.log main; # 指定访问日志文件路径及使用的日志格式
    error_log /var/log/nginx/error.log notice; # 指定错误日志文件路径及日志级别

    # HTTP连接
    keepalive_requests 100000; # 设置保持连接的最大请求数
    limit_conn_zone $binary_remote_addr zone=perip:10m; # 基于客户端 IP 的连接限制
    limit_conn_zone $server_name zone=perserver:10m; # 基于服务器名称的连接限制

    # HTTP请求
    client_header_buffer_size 32k; # 缓冲客户端请求头的内存大小
    client_max_body_size 50m; # 限制文件上传大小
    client_body_buffer_size 512k;

    # HTTP响应
    # expires 3d; # 禁用静态资源缓存
    gzip on; # 启用 Gzip 压缩
    gzip_min_length 1k; # 设置启用压缩的最小响应体长度
    gzip_buffers 4 16k; # 设置 Gzip 压缩缓冲区
    gzip_comp_level 2; # 设置 Gzip 压缩级别
    gzip_types text/plain application/javascript text/css application/xml application/json text/xml ;# 指定启用 Gzip 压缩的 MIME 类型
    gzip_vary on; # 添加 Vary: Accept-Encoding 响应头
    gzip_proxied expired no-cache no-store private auth; # 指定何时压缩代理响应
    gzip_disable "MSIE [1-6]\."; # 当客户端的 User-Agent 识别为旧版本的 Internet Explorer（IE）时，禁用 Gzip 压缩。

    # 反向代理：内存缓冲
    proxy_buffer_size 32k; # 反代响应头的缓存
    proxy_buffers 4 64k; # 反代响应体的缓存

    # 反向代理 错误处理
    proxy_next_upstream error timeout invalid_header http_500 http_503 http_404; # 设置下一步上游服务器处理的错误条件
}