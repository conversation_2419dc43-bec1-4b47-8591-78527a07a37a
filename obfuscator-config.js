module.exports = {
	// == 混淆级别控制 ==
	compact: true, // 压缩代码，生成更小的代码体积
	controlFlowFlattening: false, // 是否启用控制流平坦化，增加代码复杂度
	controlFlowFlatteningThreshold: 0.75, // 控制流平坦化的应用概率
	deadCodeInjection: false, // 是否启用无用代码注入，增加代码混淆性
	deadCodeInjectionThreshold: 0.4, // 无用代码注入的比例
	numbersToExpressions: false, // 是否将数字转换为更复杂的表达式
	renameGlobals: false, // 是否重命名全局变量
	renameProperties: false, // 是否重命名对象属性
	renamePropertiesMode: 'safe', // 属性重命名模式，默认 'safe' 以避免冲突
	transformObjectKeys: false, // 是否转换对象的键
	simplify: true, // 是否简化代码结构，移除冗余代码
	identifierNamesGenerator: 'hexadecimal', // 标识符名称生成方式，使用十六进制
	seed: 0, // 用于标识符混淆的随机种子，设为0时每次生成不同的结果

	// == 字符串混淆相关设置 ==
	stringArray: true, // 是否启用字符串数组功能，将字符串替换为数组引用
	stringArrayCallsTransform: true, // 是否转换字符串数组的调用
	stringArrayCallsTransformThreshold: 0.5, // 字符串数组调用转换的比例
	stringArrayEncoding: [], // 字符串数组的编码方式，可选 'base64' 或 'rc4'
	stringArrayIndexesType: ['hexadecimal-number'], // 字符串数组的索引类型，使用十六进制数字
	stringArrayIndexShift: true, // 是否启用字符串数组索引偏移
	stringArrayRotate: true, // 是否启用字符串数组旋转
	stringArrayShuffle: true, // 是否随机打乱字符串数组
	stringArrayWrappersCount: 1, // 包裹字符串数组的函数数量
	stringArrayWrappersChainedCalls: true, // 是否链式调用字符串数组的包装器函数
	stringArrayWrappersParametersMaxCount: 2, // 包装器函数的最大参数数量
	stringArrayWrappersType: 'variable', // 包装器函数的类型，默认为 'variable'
	stringArrayThreshold: 0.75, // 字符串数组化的概率
	splitStrings: false, // 是否将字符串拆分成多个子串
	splitStringsChunkLength: 10, // 拆分字符串时的最小长度

	// == 调试与保护 ==
	debugProtection: true, // 禁用调试工具，如F12等（增加运行时开销）
	debugProtectionInterval: 2000, // 设置 debugProtection 的间隔时间
	disableConsoleOutput: true, // 是否禁用 console 输出
	selfDefending: true, // 是否启用自我防御，防止代码被篡改

	// == 资源与 Source Map ==
	sourceMap: false, // 是否生成 source map 文件
	sourceMapBaseUrl: '', // source map 的基础 URL
	sourceMapFileName: '', // source map 文件的名称
	sourceMapMode: 'separate', // source map 的生成模式，默认为 'separate'
	sourceMapSourcesMode: 'sources-content', // source map 中 sources 字段的模式

	// == 其他设置 ==
	log: false, // 是否启用日志记录
	inputFileName: '', // 输入文件的名称（可选）
	optionsPreset: 'default', // 配置预设，默认为 'default'
	reservedNames: [], // 保留不被混淆的名称
	reservedStrings: [], // 保留不被混淆的字符串
	ignoreImports: false, // 是否忽略 `import` 语句
	domainLock: [], // 限制脚本运行的域名列表
	domainLockRedirectUrl: 'about:blank', // 当域名不匹配时重定向的 URL
	target: 'browser', // 目标环境为浏览器
	unicodeEscapeSequence: false // 是否将字符串转换为 Unicode 转义序列
};
