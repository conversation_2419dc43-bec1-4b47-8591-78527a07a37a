import axios from 'axios';

const axiosInstance = axios.create();

//发送请求前，如果浏览器的localStorage保存了token，就放到请求头中发送给服务器
axiosInstance.interceptors.request.use(
	config => {
		let token = localStorage.getItem('token');
		if (token) {
			config.headers.Authorization = 'Bearer ' + token;
		}
		return config;
	},
	err => err
);

axiosInstance.interceptors.response.use(
	res => {
		switch (res.data.state) {
			case 'success':
				return res.data.data;
			case 'fail':
				return Promise.reject(res.data.message);
			default:
				return res.data;
		}
	},
	error => {
		if (error.response) {
			// 请求已发出，但服务器响应了状态码非2xx
			// console.error("响应错误数据：", error.response.data);
			return Promise.reject(
				`响应错误，状态码：${
					error.response.status
				}，数据：${JSON.stringify(error.response.data)}`
			);
			// console.log(error.response)
		} else if (error.request) {
			// 请求已发出但没有收到响应
			// console.error("请求错误，无响应：", error.request);
			return Promise.reject('请求错误，无响应');
		} else {
			// 发送请求时发生错误
			// console.error("请求错误：", error.message);
			return Promise.reject(`发送请求时发生错误,${error.message}`);
		}
	}
);

export default axiosInstance;
