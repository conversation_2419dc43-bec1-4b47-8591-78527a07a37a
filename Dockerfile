# 构建阶段 AS build是一个别名，后续的构建阶段中，可以通过这个名称来引用该阶段的构建产物。
FROM node:20 AS build

# 设置工作目录
WORKDIR /app

#你把 WORKDIR /app 理解为“容器里所有命令的起点目录”，而 COPY 的第一个参数则是“从主机上复制文件到容器内”，这两者并不是一回事。
# 复制 package.json 和 package-lock.json
COPY package*.json ./

# 安装项目依赖
RUN npm install

# 复制项目文件到工作目录
COPY . .

# 构建 Vue 应用
RUN npm run build

# 代码混淆
RUN npm run obfuscate

# 第二阶段：以 nginx:alpine 为基础镜像，再通过 COPY --from=build 从上一阶段中拷贝已经构建好的文件。
FROM nginx:alpine

RUN mkdir -p /www/sites/courseWeb/index

# 创建用于容器挂载的目录，以防止出错
RUN mkdir -p /www/sites/courseWeb/log
RUN mkdir -p /www/sites/courseWeb/upload
RUN mkdir -p /www/sites/courseWeb/dist
RUN mkdir -p /etc/nginx/custom


# 复制构建后的文件到 Nginx 的默认目录
COPY --from=build /app/dist /www/sites/courseWeb/index

#  把nginx的基础配置放到容器中
COPY config/nginx/ /etc/nginx/

# 复制自定义的 Nginx 配置文件到容器中
COPY docker-entrypoint.sh /docker-entrypoint.sh

# 给 docker-entrypoint.sh 赋予执行权限
RUN chmod +x /docker-entrypoint.sh

# 容器启动时执行脚本
ENTRYPOINT ["/docker-entrypoint.sh"]


# 暴露端口
EXPOSE 80

# 启动 Nginx
CMD ["nginx", "-g", "daemon off;"]