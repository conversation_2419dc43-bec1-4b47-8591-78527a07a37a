import axios from "@/utils/axios.js";
import apiPath from "../../config/apiPath";

let {examApiPath}=apiPath

// 获取任务列表
export let getExamList=(data)=>axios.post(examApiPath.getExamList,data)

// 添加任务
export let addExamTask=(data)=>axios.post(examApiPath.addExamTask,data)

// 删除 任务
export let delExamTask=(data)=>axios.post(examApiPath.delExamTask,data)

// 修改任务
export let updateExamTask=(data)=>axios.post(examApiPath.updateExamTask,data)

// 开始任务
export let runExamTask=(data)=>axios.post(examApiPath.runExamTask,data)

// 清空日志
export let clearExamLog=data=>axios.post(examApiPath.clearExamLog,data)

// 获取日志
export let getExamLog=data=>axios.post(examApiPath.getExamLog,data)

// 上传照片
export const uploadPhoto = (data) => {
    return axios.post(examApiPath.uploadPhoto, data)
}

// 获取考试统计数据
export const getExamStat = () => {
    return axios.get(examApiPath.getExamStat)
}