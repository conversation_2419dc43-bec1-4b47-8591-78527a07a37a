# 排除 node_modules 目录，除非你明确需要将其复制到镜像中
node_modules

# 排除 npm 调试日志文件
npm-debug.log
yarn-debug.log
yarn-error.log

# 排除环境配置文件（如果你不想将它们包含在镜像中）
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 排除临时文件和目录
*.tmp
*.temp
tmp/
temp/
*.swp
*.swo

# 排除本地开发工具的配置文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 排除 Git 相关文件
.git/
.gitignore
.gitattributes

# 排除 Docker 文件（防止嵌套构建）
.dockerignore
Dockerfile
Dockerfile.*

# 排除操作系统生成的文件
.DS_Store
Thumbs.db

# 排除构建产物
dist/
build/
coverage/

# 排除测试文件
tests/
test/
__tests__/
__mocks__/
__snapshots__/
*.spec.js
*.test.js

# 排除文档文件
docs/
*.md

# 排除日志文件
*.log
logs/
