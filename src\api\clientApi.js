import axios from '@/utils/axios.js';
import apiPath from '../../config/apiPath';

let { clientApiPath } = apiPath;

// 获取客户端列表
export let getClientList = data => axios.get(clientApiPath.getClientList);

// 删除客户端实例
export let delClient = data => axios.post(clientApiPath.delClient, data);


// 获取客户端任务列队
export let getClientQueue = data =>
	axios.post(clientApiPath.getClientQueue, data);

// 删除客户端任务列队
export let delClientQueue = data => axios.post(clientApiPath.delClientQueue, data);