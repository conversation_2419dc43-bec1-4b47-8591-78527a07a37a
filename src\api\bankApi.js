import axios from '@/utils/axios.js';
import apiPath from '../../config/apiPath';

let { bankApiPath } = apiPath;

//1.获取题库列表
export let getBankList = data => axios.post(bankApiPath.getBankList, data);

//2.新增题目
export let addQuestion = data => axios.post(bankApiPath.addQuestion, data);

//3.修改题目
export let alertQuestion = data => axios.post(bankApiPath.alertQuestion, data);

//4.删除题目
export let deleteQuestion = data => axios.post(bankApiPath.deleteQuestion, data);

