let baseUrl = '/api';

let apiPath = {};

//用户模块
apiPath.userApiPath = {
    //用户登录
    userSignin: "/admin/user/signin",
    //根据token获取用户菜单
    getUserMenus: "/admin/user/getMenus",
    //token登录
    userValidate: "/admin/user/validate",
    //获取用户列表
    getUserList: "/admin/user/getList",
    //创建用户
    createUser: "/admin/user/create",
    //删除用户
    deleteUser: "/admin/user/delete",
    //修改用户
    updateUser: "/admin/user/update",
};

//网课模块
apiPath.courseApiPath = {
    //获取任务列表
    getCourseList: "/course/getList",
    //添加一个任务
    addCourseTask: "/course/add",
    //删除一个任务
    delCourseTask: "/course/del",
    //修改一个任务信息
    updateCourseTask: "/course/update",
    //开始一个任务
    runCourseTask: "/course/run",
    //提交图片的base64编码
    uploadImage: "/course/uploadImage",
    //获取用户头像的base64编码
    getImage: "/course/getImage",
    //清空上课任务的日志
    clearCourseLog: "/course/clearLog",
    //获取具体的任务日志
    getCourseLog: "/course/getCourseLog",
    //获取统计数据
    getCourseStat: "/course/getStat",
};

//考试模块
apiPath.examApiPath = {
    // 获取教务任务列表
    getExamList: "/exam/getList",
    // 添加一个任务
    addExamTask: "/exam/add",
    // 删除一个任务
    delExamTask: "/exam/del",
    // 修改一个任务信息
    updateExamTask: "/exam/update",
    // 开始一个任务
    runExamTask: "/exam/run",
    // 清空任务的日志
    clearExamLog: "/exam/clearLog",
    // 上传头像到本地
    uploadPhoto:'/exam/uploadPhoto',
    // 获取考试日志
    getExamLog:'/exam/getExamLog',
    // 获取统计数据
    getExamStat: "/exam/getStat"
};

//题库模块
apiPath.bankApiPath = {
    //获取题库列表
    getBankList: "/bank/getList",
    //新增题目
    addQuestion: "/bank/add",
    //修改题目
    alertQuestion: "/bank/alert",
    //删除题目
    deleteQuestion: "/bank/delete",
};

//进程模块
apiPath.clusterApiPath = {
	//获取进程列表
	getClusterList: '/cluster/getList',
	//添加一个进程
	addCluster: '/cluster/add',
	//删除一个进程
	delCluster: '/cluster/del',
	//重置进程
	resetCluster: '/cluster/resetCluster',
    //根据任务id返回pcid和workId
    getClusterInfoByTaskId: '/cluster/getClusterInfoByTaskId',
    //获取统计数据
    getClusterStat: '/cluster/getStat'
};

//代理模块
apiPath.proxyApiPath = {
    //获取代理列表
    getProxyList: "/proxy/getList",
    //批量添加代理
    addProxy: "/proxy/add",
    //批量删除代理
    delProxy: "/proxy/del",
    //修改代理信息
    updateProxy: "/proxy/update"
};

//权限模块
apiPath.permissionApiPath = {
    //获取权限树形结构
    getPermissionTree: "/admin/permission/getTree",
    //获取权限列表（平铺结构）
    getPermissionList: "/admin/permission/getList",
    //创建权限
    createPermission: "/admin/permission/create",
    //修改权限
    updatePermission: "/admin/permission/update",
    //删除权限
    deletePermission: "/admin/permission/delete"
};



// 客户端模块
apiPath.clientApiPath={
    //获取客户端列表
    getClientList:'/client/getClientList',
    //获取客户端任务列队
    getClientQueue:'/client/getClientQueue',
    //删除客户端实例
    delClient:'/client/del',
    //删除客户端对应的任务
    delClientQueue:'/client/delQueue'
}

// 其他模块
apiPath.otherApiPath = {
    //返回用户IP
    getUserIp: "/other/getUserIp",
}


function addBaseUrl(arr) {
	arr.forEach(obj => {
		Object.keys(obj).forEach(key => {
			obj[key] = baseUrl + obj[key];
		});
	});
}

let apiArr = Object.values(apiPath);

let apiPathArr = [...apiArr];
addBaseUrl(apiPathArr);

export default apiPath;
