services:
    courseWeb: 

        # docker run --name ${CONTAINER_NAME} [OPTIONS] 1panel/openresty:********-3-3-focal
        container_name: courseWeb  # 容器名称由环境变量 CONTAINER_NAME 决定

        # docker run [OPTIONS] 1panel/openresty:********-3-3-focal
        image: course-web

        # docker run --network host [OPTIONS] 1panel/openresty:********-3-3-focal
        network_mode: bridge  # 使用主机网络模式，容器将直接共享主机的网络栈（例如，容器将与主机使用相同的 IP 地址）

        # docker run --restart always [OPTIONS] 1panel/openresty:********-3-3-focal
        # restart: always  # 容器设置为始终重启，即使在失败后也会自动重启

        # docker run -p ${HOST_IP}:${PANEL_APP_PORT_HTTP}:3306 [OPTIONS] mysql:8.2.0
        ports:
            - 80:80  
        
        environment:
            - ENV=${ENV}

        # docker run -v ./conf/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
        #            [OPTIONS] 1panel/openresty:********-3-3-focal
        volumes:
            # 分割符号的左边是 本地目录，右边是 容器目录。
            # nginx日志目录
            - ${LOG_PATH}:/www/sites/courseWeb/log
            
            # 头像上传目录
            - ${UPLOAD_PATH}:/www/sites/courseWeb/upload

            # 打包后的项目  只有DEV才需要挂载，另外两个环境不需要
            - ${DIST_PATH}:/www/sites/courseWeb/dist

            # nginx自定义配置目录
            - ${NGINX_CONFIG_PATH}:/etc/nginx/custom