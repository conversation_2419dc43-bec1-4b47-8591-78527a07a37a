# HTTP 网站
server {
    # web服务器设置
    server_name course.mozhi0012.top; # 服务器名称，可以是域名或 IP 地址。
    listen 80 ; # 监听端口

    # 日志配置
    access_log /www/sites/courseWeb/log/access.log; # 访问日志文件路径及日志格式
    error_log /www/sites/courseWeb/log/error.log; # 错误日志文件路径 error日志不接受日志级别

    # 路由配置：反向代理配置 prod
    location ^~/api/ {
        # 容器不能通过127.0.0.1访问主机，可以使用host.docker.internal
        # host.docker.internal 是在 macOS 和 Windows 中 Docker 的默认名称，用来指向宿主机。要在 Linux 中启用它，你需要在 Docker 运行时手动定义这个主机名。
        # 可以直接写如linunx的Host echo "************* host.docker.internal" >> /etc/hosts
        proxy_pass http://***********:3000;
        proxy_http_version 1.1; # 设置 HTTP 协议版本为 1.1

        # 保留客户端请求的 Host、IP等信息，传递给后端
        proxy_set_header Host $host; # 原始客户端请求的 Host(域名)
        proxy_set_header X-Real-IP $remote_addr; # 客户端的真实 IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 代理链上的所有 IP
        proxy_set_header X-Forwarded-Host $host; # 也可用 $server_name，看后端如何解析
        # 网站设置
        # error_page 502 /backend_down.html; # 当发生 502 错误时返回 backend_down.html 页
    }

    # 路由配置：反向代理配置 prod
    location /ws {
        proxy_pass http://***********:3000;
        proxy_http_version 1.1; # 设置 HTTP 协议版本为 1.1

        # 保留客户端请求的 Host、IP等信息，传递给后端
        proxy_set_header Host $host; # 原始客户端请求的 Host(域名)
        proxy_set_header X-Real-IP $remote_addr; # 客户端的真实 IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 代理链上的所有 IP
        proxy_set_header X-Forwarded-Host $host; # 也可用 $server_name，看后端如何解析

        proxy_set_header Upgrade $http_upgrade; # 允许协议升级
        proxy_set_header Connection "upgrade"; # 允许连接升级
        proxy_set_header Host $host; # 透传Host等请求头
    }


    # history模式 try_files指令，用来尝试按顺序找到第一个存在的文件，并返回它。
    # $uri：这是Nginx的一个内置变量，表示请求的URI，
    location / {
        root /www/sites/courseWeb/index; # 网站资源根目录
        try_files $uri $uri/ /index.html;
        index index.php index.html index.htm default.php default.htm default.html; # 默认的索引文件列表
        # error_page 404 /404.html; # 自定义 404 错误页面

    }

    # root 会把url中的path和root配置拼接起来
    # alias 等于url中的path减去loction配置，然后加上alias配置
    location ^~/avatar/ {
        root /www/sites/courseWeb/upload; # 末尾不能加 /
    }
}


# HTTPS 网站
server {
    # web服务器设置
    listen 443 ssl;
    server_name course.mozhi0012.top; # 替换为你的域名

    # SSL 证书和私钥的路径
    ssl_certificate /etc/nginx/custom/fullchain.pem;
    ssl_certificate_key /etc/nginx/custom/privkey.pem;

    # 可选的 SSL 相关配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;
    ssl_prefer_server_ciphers on;

    # 日志配置
    access_log /www/sites/courseWeb/log/access.log; # 访问日志文件路径及日志格式
    error_log /www/sites/courseWeb/log/error.log; # 错误日志文件路径 error日志不接受日志级别


    # history模式 try_files指令，用来尝试按顺序找到第一个存在的文件，并返回它。
    # $uri：这是Nginx的一个内置变量，表示请求的URI，
    location / {
        root /www/sites/courseWeb/index; # 网站资源根目录
        try_files $uri $uri/ /index.html;
        index index.php index.html index.htm default.php default.htm default.html; # 默认的索引文件列表
        # error_page 404 /404.html; # 自定义 404 错误页面
    }

    # 路由配置：反向代理配置 prod
    location ^~/api/ {
        # 容器不能通过127.0.0.1访问主机，可以使用host.docker.internal
        # host.docker.internal 是在 macOS 和 Windows 中 Docker 的默认名称，用来指向宿主机。要在 Linux 中启用它，你需要在 Docker 运行时手动定义这个主机名。
        # 可以直接写如linunx的Host echo "************* host.docker.internal" >> /etc/hosts
        proxy_pass http://**************:3000;
        proxy_http_version 1.1; # 设置 HTTP 协议版本为 1.1

        # 保留客户端请求的 Host、IP等信息，传递给后端
        proxy_set_header Host $host; # 原始客户端请求的 Host(域名)
        proxy_set_header X-Real-IP $remote_addr; # 客户端的真实 IP
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; # 代理链上的所有 IP
        proxy_set_header X-Forwarded-Host $host; # 也可用 $server_name，看后端如何解析
        # 网站设置
        # error_page 502 /backend_down.html; # 当发生 502 错误时返回 backend_down.html 页
    }

    # root 会把url中的path和root配置拼接起来
    # alias 等于url中的path减去loction配置，然后加上alias配置
    location ^~/avatar/ {
        root /www/sites/courseWeb/upload; # 末尾不能加 /
    }
}


# 反代 https://api.oaipro.com
server {
    listen 80 ;
    server_name oaipro.mozhi0012.top;
    index index.php index.html index.htm default.php default.htm default.html;
    proxy_set_header Host $host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Host $server_name;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection $http_connection;

    # 日志配置
    access_log /www/sites/courseWeb/log/oaipro_access.log; # 访问日志文件路径及日志格式
    error_log /www/sites/courseWeb/log/oaipro_error.log; # 错误日志文件路径 error日志不接受日志级别


    location ^~/ {
        proxy_pass https://api.oaipro.com;
        proxy_set_header Host api.oaipro.com;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $http_connection;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        add_header X-Cache $upstream_cache_status;
        add_header Cache-Control no-cache;
        proxy_ssl_server_name on;
        proxy_ssl_protocols TLSv1.2 TLSv1.3;
        proxy_ssl_ciphers HIGH:!aNULL:!MD5;
    }
}